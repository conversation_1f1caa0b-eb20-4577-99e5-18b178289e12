{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  fetchUserPromotions: () => {\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);\n  },\n  // Claim promotion\n  claimPromotion: data => {\n    return api.post(ApiConstants.CLAIM_PROMOTION, data);\n  },\n  // Fetch all promotions for modal (public endpoint)\n  fetchAllPromotions: () => {\n    // Note: totalPrice filtering will be done in saga after fetching all promotions\n    return api.get(ApiConstants.FETCH_ALL_PROMOTIONS);\n  },\n  // Apply promotion\n  applyPromotion: data => {\n    return api.post(ApiConstants.USE_PROMOTION, data);\n  },\n  // Legacy method (keep for backward compatibility)\n  usePromotion: data => {\n    return api.post(ApiConstants.USE_PROMOTION, data);\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "fetchUserPromotions", "get", "FETCH_USER_PROMOTIONS", "claimPromotion", "data", "post", "CLAIM_PROMOTION", "fetchAllPromotions", "FETCH_ALL_PROMOTIONS", "applyPromotion", "USE_PROMOTION", "usePromotion"], "sources": ["E:/Uroom/Customer/src/redux/promotion/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  fetchUserPromotions: () => {\r\n    return api.get(ApiConstants.FETCH_USER_PROMOTIONS);\r\n  },\r\n\r\n  // Claim promotion\r\n  claimPromotion: (data) => {\r\n    return api.post(ApiConstants.CLAIM_PROMOTION, data);\r\n  },\r\n\r\n  // Fetch all promotions for modal (public endpoint)\r\n  fetchAllPromotions: () => {\r\n    // Note: totalPrice filtering will be done in saga after fetching all promotions\r\n    return api.get(ApiConstants.FETCH_ALL_PROMOTIONS);\r\n  },\r\n\r\n  // Apply promotion\r\n  applyPromotion: (data) => {\r\n    return api.post(ApiConstants.USE_PROMOTION, data);\r\n  },\r\n\r\n  // Legacy method (keep for backward compatibility)\r\n  usePromotion: (data) => {\r\n    return api.post(ApiConstants.USE_PROMOTION, data);\r\n  },\r\n};\r\n\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChBC,mBAAmB,EAAEA,CAAA,KAAM;IACzB,OAAOF,GAAG,CAACG,GAAG,CAACJ,YAAY,CAACK,qBAAqB,CAAC;EACpD,CAAC;EAED;EACAC,cAAc,EAAGC,IAAI,IAAK;IACxB,OAAON,GAAG,CAACO,IAAI,CAACR,YAAY,CAACS,eAAe,EAAEF,IAAI,CAAC;EACrD,CAAC;EAED;EACAG,kBAAkB,EAAEA,CAAA,KAAM;IACxB;IACA,OAAOT,GAAG,CAACG,GAAG,CAACJ,YAAY,CAACW,oBAAoB,CAAC;EACnD,CAAC;EAED;EACAC,cAAc,EAAGL,IAAI,IAAK;IACxB,OAAON,GAAG,CAACO,IAAI,CAACR,YAAY,CAACa,aAAa,EAAEN,IAAI,CAAC;EACnD,CAAC;EAED;EACAO,YAAY,EAAGP,IAAI,IAAK;IACtB,OAAON,GAAG,CAACO,IAAI,CAACR,YAAY,CAACa,aAAa,EAAEN,IAAI,CAAC;EACnD;AACF,CAAC;AAED,eAAeL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}