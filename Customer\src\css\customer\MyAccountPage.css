/* User profile styling */
.sidebar {
    border-radius: 8px;
    overflow: hidden;
  }
  
  .user-profile {
    padding: 20px 0;
    background-color: #f8f9fa;
  }
  
  .avatar-circle {
    width: 70px;
    height: 70px;
    background-color: #e9ecef;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }
  
  .avatar-icon {
    font-size: 30px;
    color: #6c757d;
  }
  
  .menu-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-left: 3px solid transparent;
  }
  
  .menu-item:hover {
    background-color: #f1f3f5;
  }
  
  .menu-item.active {
    background-color: #0d6efd;
    color: white;
    border-left: 3px solid #0a58ca;
  }
  
  .menu-icon {
    margin-right: 10px;
    font-size: 18px;
    width: 24px;
    text-align: center;
  }
  
  /* Form styling */
  .card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border: none;
  }
  
  /* Make the form look more like the design */
  .form-control {
    border-radius: 4px;
    padding: 8px 12px;
  }
  
  .form-control::placeholder {
    color: #adb5bd;
    font-size: 14px;
  }
  
  .form-label {
    font-weight: 500;
    margin-bottom: 6px;
  }
  
  .input-group-text {
    background-color: white;
    border-left: none;
  }
  
  