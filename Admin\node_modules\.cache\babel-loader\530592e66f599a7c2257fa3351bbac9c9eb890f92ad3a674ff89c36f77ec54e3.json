{"ast": null, "code": "const PromotionActions = {\n  // Fetch all promotions\n  FETCH_ALL_PROMOTIONS: \"FETCH_ALL_PROMOTIONS\",\n  FETCH_ALL_PROMOTIONS_SUCCESS: \"FETCH_ALL_PROMOTIONS_SUCCESS\",\n  FETCH_ALL_PROMOTIONS_FAILURE: \"FETCH_ALL_PROMOTIONS_FAILURE\",\n  // Create promotion\n  CREATE_PROMOTION: \"CREATE_PROMOTION\",\n  CREATE_PROMOTION_SUCCESS: \"CREATE_PROMOTION_SUCCESS\",\n  CREATE_PROMOTION_FAILURE: \"CREATE_PROMOTION_FAILURE\",\n  // Update promotion\n  UPDATE_PROMOTION: \"UPDATE_PROMOTION\",\n  UPDATE_PROMOTION_SUCCESS: \"UPDATE_PROMOTION_SUCCESS\",\n  UPDATE_PROMOTION_FAILURE: \"UPDATE_PROMOTION_FAILURE\",\n  // Delete promotion\n  DELETE_PROMOTION: \"DELETE_PROMOTION\",\n  DELETE_PROMOTION_SUCCESS: \"DELETE_PROMOTION_SUCCESS\",\n  DELETE_PROMOTION_FAILURE: \"DELETE_PROMOTION_FAILURE\",\n  // Get promotion by ID\n  GET_PROMOTION_BY_ID: \"GET_PROMOTION_BY_ID\",\n  GET_PROMOTION_BY_ID_SUCCESS: \"GET_PROMOTION_BY_ID_SUCCESS\",\n  GET_PROMOTION_BY_ID_FAILURE: \"GET_PROMOTION_BY_ID_FAILURE\",\n  // Toggle promotion status\n  TOGGLE_PROMOTION_STATUS: \"TOGGLE_PROMOTION_STATUS\",\n  TOGGLE_PROMOTION_STATUS_SUCCESS: \"TOGGLE_PROMOTION_STATUS_SUCCESS\",\n  TOGGLE_PROMOTION_STATUS_FAILURE: \"TOGGLE_PROMOTION_STATUS_FAILURE\",\n  // Clear errors\n  CLEAR_PROMOTION_ERROR: \"CLEAR_PROMOTION_ERROR\",\n  // Pagination and filters\n  SET_PROMOTION_FILTERS: \"SET_PROMOTION_FILTERS\",\n  SET_PROMOTION_PAGINATION: \"SET_PROMOTION_PAGINATION\",\n  RESET_PROMOTION_FILTERS: \"RESET_PROMOTION_FILTERS\",\n  // Promotion User Management\n  GET_PROMOTION_USERS: \"GET_PROMOTION_USERS\",\n  GET_PROMOTION_USERS_SUCCESS: \"GET_PROMOTION_USERS_SUCCESS\",\n  GET_PROMOTION_USERS_FAILURE: \"GET_PROMOTION_USERS_FAILURE\",\n  GET_PROMOTION_STATS: \"GET_PROMOTION_STATS\",\n  GET_PROMOTION_STATS_SUCCESS: \"GET_PROMOTION_STATS_SUCCESS\",\n  GET_PROMOTION_STATS_FAILURE: \"GET_PROMOTION_STATS_FAILURE\",\n  GET_USER_PROMOTIONS: \"GET_USER_PROMOTIONS\",\n  GET_USER_PROMOTIONS_SUCCESS: \"GET_USER_PROMOTIONS_SUCCESS\",\n  GET_USER_PROMOTIONS_FAILURE: \"GET_USER_PROMOTIONS_FAILURE\",\n  REMOVE_USER_FROM_PROMOTION: \"REMOVE_USER_FROM_PROMOTION\",\n  REMOVE_USER_FROM_PROMOTION_SUCCESS: \"REMOVE_USER_FROM_PROMOTION_SUCCESS\",\n  REMOVE_USER_FROM_PROMOTION_FAILURE: \"REMOVE_USER_FROM_PROMOTION_FAILURE\",\n  RESET_USER_PROMOTION_USAGE: \"RESET_USER_PROMOTION_USAGE\",\n  RESET_USER_PROMOTION_USAGE_SUCCESS: \"RESET_USER_PROMOTION_USAGE_SUCCESS\",\n  RESET_USER_PROMOTION_USAGE_FAILURE: \"RESET_USER_PROMOTION_USAGE_FAILURE\",\n  // Assign Promotion\n  SEARCH_USERS_FOR_ASSIGNMENT: \"SEARCH_USERS_FOR_ASSIGNMENT\",\n  SEARCH_USERS_FOR_ASSIGNMENT_SUCCESS: \"SEARCH_USERS_FOR_ASSIGNMENT_SUCCESS\",\n  SEARCH_USERS_FOR_ASSIGNMENT_FAILURE: \"SEARCH_USERS_FOR_ASSIGNMENT_FAILURE\",\n  ASSIGN_PROMOTION_TO_USERS: \"ASSIGN_PROMOTION_TO_USERS\",\n  ASSIGN_PROMOTION_TO_USERS_SUCCESS: \"ASSIGN_PROMOTION_TO_USERS_SUCCESS\",\n  ASSIGN_PROMOTION_TO_USERS_FAILURE: \"ASSIGN_PROMOTION_TO_USERS_FAILURE\"\n};\n\n// Action creators\nexport const fetchAllPromotions = params => ({\n  type: PromotionActions.FETCH_ALL_PROMOTIONS,\n  payload: params\n});\nexport const fetchAllPromotionsSuccess = data => ({\n  type: PromotionActions.FETCH_ALL_PROMOTIONS_SUCCESS,\n  payload: data\n});\nexport const fetchAllPromotionsFailure = error => ({\n  type: PromotionActions.FETCH_ALL_PROMOTIONS_FAILURE,\n  payload: error\n});\nexport const createPromotion = data => ({\n  type: PromotionActions.CREATE_PROMOTION,\n  payload: data\n});\nexport const createPromotionSuccess = data => ({\n  type: PromotionActions.CREATE_PROMOTION_SUCCESS,\n  payload: data\n});\nexport const createPromotionFailure = error => ({\n  type: PromotionActions.CREATE_PROMOTION_FAILURE,\n  payload: error\n});\nexport const updatePromotion = data => ({\n  type: PromotionActions.UPDATE_PROMOTION,\n  payload: data\n});\nexport const updatePromotionSuccess = data => ({\n  type: PromotionActions.UPDATE_PROMOTION_SUCCESS,\n  payload: data\n});\nexport const updatePromotionFailure = error => ({\n  type: PromotionActions.UPDATE_PROMOTION_FAILURE,\n  payload: error\n});\nexport const deletePromotion = data => ({\n  type: PromotionActions.DELETE_PROMOTION,\n  payload: data\n});\nexport const deletePromotionSuccess = data => ({\n  type: PromotionActions.DELETE_PROMOTION_SUCCESS,\n  payload: data\n});\nexport const deletePromotionFailure = error => ({\n  type: PromotionActions.DELETE_PROMOTION_FAILURE,\n  payload: error\n});\nexport const getPromotionById = data => ({\n  type: PromotionActions.GET_PROMOTION_BY_ID,\n  payload: data\n});\nexport const getPromotionByIdSuccess = data => ({\n  type: PromotionActions.GET_PROMOTION_BY_ID_SUCCESS,\n  payload: data\n});\nexport const getPromotionByIdFailure = error => ({\n  type: PromotionActions.GET_PROMOTION_BY_ID_FAILURE,\n  payload: error\n});\nexport const togglePromotionStatus = data => ({\n  type: PromotionActions.TOGGLE_PROMOTION_STATUS,\n  payload: data\n});\nexport const togglePromotionStatusSuccess = data => ({\n  type: PromotionActions.TOGGLE_PROMOTION_STATUS_SUCCESS,\n  payload: data\n});\nexport const togglePromotionStatusFailure = error => ({\n  type: PromotionActions.TOGGLE_PROMOTION_STATUS_FAILURE,\n  payload: error\n});\nexport const clearPromotionError = () => ({\n  type: PromotionActions.CLEAR_PROMOTION_ERROR\n});\nexport const setPromotionFilters = filters => ({\n  type: PromotionActions.SET_PROMOTION_FILTERS,\n  payload: filters\n});\nexport const setPromotionPagination = pagination => ({\n  type: PromotionActions.SET_PROMOTION_PAGINATION,\n  payload: pagination\n});\nexport const resetPromotionFilters = () => ({\n  type: PromotionActions.RESET_PROMOTION_FILTERS\n});\n\n// Promotion User Management Action Creators\nexport const getPromotionUsers = params => ({\n  type: PromotionActions.GET_PROMOTION_USERS,\n  payload: params\n});\nexport const getPromotionUsersSuccess = data => ({\n  type: PromotionActions.GET_PROMOTION_USERS_SUCCESS,\n  payload: data\n});\nexport const getPromotionUsersFailure = error => ({\n  type: PromotionActions.GET_PROMOTION_USERS_FAILURE,\n  payload: error\n});\nexport const getPromotionStats = params => ({\n  type: PromotionActions.GET_PROMOTION_STATS,\n  payload: params\n});\nexport const getPromotionStatsSuccess = data => ({\n  type: PromotionActions.GET_PROMOTION_STATS_SUCCESS,\n  payload: data\n});\nexport const getPromotionStatsFailure = error => ({\n  type: PromotionActions.GET_PROMOTION_STATS_FAILURE,\n  payload: error\n});\nexport const getUserPromotions = params => ({\n  type: PromotionActions.GET_USER_PROMOTIONS,\n  payload: params\n});\nexport const getUserPromotionsSuccess = data => ({\n  type: PromotionActions.GET_USER_PROMOTIONS_SUCCESS,\n  payload: data\n});\nexport const getUserPromotionsFailure = error => ({\n  type: PromotionActions.GET_USER_PROMOTIONS_FAILURE,\n  payload: error\n});\nexport const removeUserFromPromotion = params => ({\n  type: PromotionActions.REMOVE_USER_FROM_PROMOTION,\n  payload: params\n});\nexport const removeUserFromPromotionSuccess = data => ({\n  type: PromotionActions.REMOVE_USER_FROM_PROMOTION_SUCCESS,\n  payload: data\n});\nexport const removeUserFromPromotionFailure = error => ({\n  type: PromotionActions.REMOVE_USER_FROM_PROMOTION_FAILURE,\n  payload: error\n});\nexport const resetUserPromotionUsage = params => ({\n  type: PromotionActions.RESET_USER_PROMOTION_USAGE,\n  payload: params\n});\nexport const resetUserPromotionUsageSuccess = data => ({\n  type: PromotionActions.RESET_USER_PROMOTION_USAGE_SUCCESS,\n  payload: data\n});\nexport const resetUserPromotionUsageFailure = error => ({\n  type: PromotionActions.RESET_USER_PROMOTION_USAGE_FAILURE,\n  payload: error\n});\nexport default PromotionActions;", "map": {"version": 3, "names": ["PromotionActions", "FETCH_ALL_PROMOTIONS", "FETCH_ALL_PROMOTIONS_SUCCESS", "FETCH_ALL_PROMOTIONS_FAILURE", "CREATE_PROMOTION", "CREATE_PROMOTION_SUCCESS", "CREATE_PROMOTION_FAILURE", "UPDATE_PROMOTION", "UPDATE_PROMOTION_SUCCESS", "UPDATE_PROMOTION_FAILURE", "DELETE_PROMOTION", "DELETE_PROMOTION_SUCCESS", "DELETE_PROMOTION_FAILURE", "GET_PROMOTION_BY_ID", "GET_PROMOTION_BY_ID_SUCCESS", "GET_PROMOTION_BY_ID_FAILURE", "TOGGLE_PROMOTION_STATUS", "TOGGLE_PROMOTION_STATUS_SUCCESS", "TOGGLE_PROMOTION_STATUS_FAILURE", "CLEAR_PROMOTION_ERROR", "SET_PROMOTION_FILTERS", "SET_PROMOTION_PAGINATION", "RESET_PROMOTION_FILTERS", "GET_PROMOTION_USERS", "GET_PROMOTION_USERS_SUCCESS", "GET_PROMOTION_USERS_FAILURE", "GET_PROMOTION_STATS", "GET_PROMOTION_STATS_SUCCESS", "GET_PROMOTION_STATS_FAILURE", "GET_USER_PROMOTIONS", "GET_USER_PROMOTIONS_SUCCESS", "GET_USER_PROMOTIONS_FAILURE", "REMOVE_USER_FROM_PROMOTION", "REMOVE_USER_FROM_PROMOTION_SUCCESS", "REMOVE_USER_FROM_PROMOTION_FAILURE", "RESET_USER_PROMOTION_USAGE", "RESET_USER_PROMOTION_USAGE_SUCCESS", "RESET_USER_PROMOTION_USAGE_FAILURE", "SEARCH_USERS_FOR_ASSIGNMENT", "SEARCH_USERS_FOR_ASSIGNMENT_SUCCESS", "SEARCH_USERS_FOR_ASSIGNMENT_FAILURE", "ASSIGN_PROMOTION_TO_USERS", "ASSIGN_PROMOTION_TO_USERS_SUCCESS", "ASSIGN_PROMOTION_TO_USERS_FAILURE", "fetchAllPromotions", "params", "type", "payload", "fetchAllPromotionsSuccess", "data", "fetchAllPromotionsFailure", "error", "createPromotion", "createPromotionSuccess", "createPromotionFailure", "updatePromotion", "updatePromotionSuccess", "updatePromotionFailure", "deletePromotion", "deletePromotionSuccess", "deletePromotionFailure", "getPromotionById", "getPromotionByIdSuccess", "getPromotionByIdFailure", "togglePromotionStatus", "togglePromotionStatusSuccess", "togglePromotionStatusFailure", "clearPromotionError", "setPromotionFilters", "filters", "setPromotionPagination", "pagination", "resetPromotionFilters", "getPromotionUsers", "getPromotionUsersSuccess", "getPromotionUsersFailure", "getPromotionStats", "getPromotionStatsSuccess", "getPromotionStatsFailure", "getUserPromotions", "getUserPromotionsSuccess", "getUserPromotionsFailure", "removeUserFromPromotion", "removeUserFromPromotionSuccess", "removeUserFromPromotionFailure", "resetUserPromotionUsage", "resetUserPromotionUsageSuccess", "resetUserPromotionUsageFailure"], "sources": ["E:/Uroom/Admin/src/redux/promotion/actions.js"], "sourcesContent": ["const PromotionActions = {\r\n  // Fetch all promotions\r\n  FETCH_ALL_PROMOTIONS: \"FETCH_ALL_PROMOTIONS\",\r\n  FETCH_ALL_PROMOTIONS_SUCCESS: \"FETCH_ALL_PROMOTIONS_SUCCESS\",\r\n  FETCH_ALL_PROMOTIONS_FAILURE: \"FETCH_ALL_PROMOTIONS_FAILURE\",\r\n\r\n  // Create promotion\r\n  CREATE_PROMOTION: \"CREATE_PROMOTION\",\r\n  CREATE_PROMOTION_SUCCESS: \"CREATE_PROMOTION_SUCCESS\",\r\n  CREATE_PROMOTION_FAILURE: \"CREATE_PROMOTION_FAILURE\",\r\n\r\n  // Update promotion\r\n  UPDATE_PROMOTION: \"UPDATE_PROMOTION\",\r\n  UPDATE_PROMOTION_SUCCESS: \"UPDATE_PROMOTION_SUCCESS\",\r\n  UPDATE_PROMOTION_FAILURE: \"UPDATE_PROMOTION_FAILURE\",\r\n\r\n  // Delete promotion\r\n  DELETE_PROMOTION: \"DELETE_PROMOTION\",\r\n  DELETE_PROMOTION_SUCCESS: \"DELETE_PROMOTION_SUCCESS\",\r\n  DELETE_PROMOTION_FAILURE: \"DELETE_PROMOTION_FAILURE\",\r\n\r\n  // Get promotion by ID\r\n  GET_PROMOTION_BY_ID: \"GET_PROMOTION_BY_ID\",\r\n  GET_PROMOTION_BY_ID_SUCCESS: \"GET_PROMOTION_BY_ID_SUCCESS\",\r\n  GET_PROMOTION_BY_ID_FAILURE: \"GET_PROMOTION_BY_ID_FAILURE\",\r\n\r\n  // Toggle promotion status\r\n  TOGGLE_PROMOTION_STATUS: \"TOGGLE_PROMOTION_STATUS\",\r\n  TOGGLE_PROMOTION_STATUS_SUCCESS: \"TOGGLE_PROMOTION_STATUS_SUCCESS\",\r\n  TOGGLE_PROMOTION_STATUS_FAILURE: \"TOGGLE_PROMOTION_STATUS_FAILURE\",\r\n\r\n  // Clear errors\r\n  CLEAR_PROMOTION_ERROR: \"CLEAR_PROMOTION_ERROR\",\r\n\r\n  // Pagination and filters\r\n  SET_PROMOTION_FILTERS: \"SET_PROMOTION_FILTERS\",\r\n  SET_PROMOTION_PAGINATION: \"SET_PROMOTION_PAGINATION\",\r\n  RESET_PROMOTION_FILTERS: \"RESET_PROMOTION_FILTERS\",\r\n\r\n  // Promotion User Management\r\n  GET_PROMOTION_USERS: \"GET_PROMOTION_USERS\",\r\n  GET_PROMOTION_USERS_SUCCESS: \"GET_PROMOTION_USERS_SUCCESS\",\r\n  GET_PROMOTION_USERS_FAILURE: \"GET_PROMOTION_USERS_FAILURE\",\r\n\r\n  GET_PROMOTION_STATS: \"GET_PROMOTION_STATS\",\r\n  GET_PROMOTION_STATS_SUCCESS: \"GET_PROMOTION_STATS_SUCCESS\",\r\n  GET_PROMOTION_STATS_FAILURE: \"GET_PROMOTION_STATS_FAILURE\",\r\n\r\n  GET_USER_PROMOTIONS: \"GET_USER_PROMOTIONS\",\r\n  GET_USER_PROMOTIONS_SUCCESS: \"GET_USER_PROMOTIONS_SUCCESS\",\r\n  GET_USER_PROMOTIONS_FAILURE: \"GET_USER_PROMOTIONS_FAILURE\",\r\n\r\n  REMOVE_USER_FROM_PROMOTION: \"REMOVE_USER_FROM_PROMOTION\",\r\n  REMOVE_USER_FROM_PROMOTION_SUCCESS: \"REMOVE_USER_FROM_PROMOTION_SUCCESS\",\r\n  REMOVE_USER_FROM_PROMOTION_FAILURE: \"REMOVE_USER_FROM_PROMOTION_FAILURE\",\r\n\r\n  RESET_USER_PROMOTION_USAGE: \"RESET_USER_PROMOTION_USAGE\",\r\n  RESET_USER_PROMOTION_USAGE_SUCCESS: \"RESET_USER_PROMOTION_USAGE_SUCCESS\",\r\n  RESET_USER_PROMOTION_USAGE_FAILURE: \"RESET_USER_PROMOTION_USAGE_FAILURE\",\r\n\r\n  // Assign Promotion\r\n  SEARCH_USERS_FOR_ASSIGNMENT: \"SEARCH_USERS_FOR_ASSIGNMENT\",\r\n  SEARCH_USERS_FOR_ASSIGNMENT_SUCCESS: \"SEARCH_USERS_FOR_ASSIGNMENT_SUCCESS\",\r\n  SEARCH_USERS_FOR_ASSIGNMENT_FAILURE: \"SEARCH_USERS_FOR_ASSIGNMENT_FAILURE\",\r\n\r\n  ASSIGN_PROMOTION_TO_USERS: \"ASSIGN_PROMOTION_TO_USERS\",\r\n  ASSIGN_PROMOTION_TO_USERS_SUCCESS: \"ASSIGN_PROMOTION_TO_USERS_SUCCESS\",\r\n  ASSIGN_PROMOTION_TO_USERS_FAILURE: \"ASSIGN_PROMOTION_TO_USERS_FAILURE\",\r\n};\r\n\r\n// Action creators\r\nexport const fetchAllPromotions = (params) => ({\r\n  type: PromotionActions.FETCH_ALL_PROMOTIONS,\r\n  payload: params\r\n});\r\n\r\nexport const fetchAllPromotionsSuccess = (data) => ({\r\n  type: PromotionActions.FETCH_ALL_PROMOTIONS_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const fetchAllPromotionsFailure = (error) => ({\r\n  type: PromotionActions.FETCH_ALL_PROMOTIONS_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const createPromotion = (data) => ({\r\n  type: PromotionActions.CREATE_PROMOTION,\r\n  payload: data\r\n});\r\n\r\nexport const createPromotionSuccess = (data) => ({\r\n  type: PromotionActions.CREATE_PROMOTION_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const createPromotionFailure = (error) => ({\r\n  type: PromotionActions.CREATE_PROMOTION_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const updatePromotion = (data) => ({\r\n  type: PromotionActions.UPDATE_PROMOTION,\r\n  payload: data\r\n});\r\n\r\nexport const updatePromotionSuccess = (data) => ({\r\n  type: PromotionActions.UPDATE_PROMOTION_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const updatePromotionFailure = (error) => ({\r\n  type: PromotionActions.UPDATE_PROMOTION_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const deletePromotion = (data) => ({\r\n  type: PromotionActions.DELETE_PROMOTION,\r\n  payload: data\r\n});\r\n\r\nexport const deletePromotionSuccess = (data) => ({\r\n  type: PromotionActions.DELETE_PROMOTION_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const deletePromotionFailure = (error) => ({\r\n  type: PromotionActions.DELETE_PROMOTION_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const getPromotionById = (data) => ({\r\n  type: PromotionActions.GET_PROMOTION_BY_ID,\r\n  payload: data\r\n});\r\n\r\nexport const getPromotionByIdSuccess = (data) => ({\r\n  type: PromotionActions.GET_PROMOTION_BY_ID_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const getPromotionByIdFailure = (error) => ({\r\n  type: PromotionActions.GET_PROMOTION_BY_ID_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const togglePromotionStatus = (data) => ({\r\n  type: PromotionActions.TOGGLE_PROMOTION_STATUS,\r\n  payload: data\r\n});\r\n\r\nexport const togglePromotionStatusSuccess = (data) => ({\r\n  type: PromotionActions.TOGGLE_PROMOTION_STATUS_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const togglePromotionStatusFailure = (error) => ({\r\n  type: PromotionActions.TOGGLE_PROMOTION_STATUS_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const clearPromotionError = () => ({\r\n  type: PromotionActions.CLEAR_PROMOTION_ERROR\r\n});\r\n\r\nexport const setPromotionFilters = (filters) => ({\r\n  type: PromotionActions.SET_PROMOTION_FILTERS,\r\n  payload: filters\r\n});\r\n\r\nexport const setPromotionPagination = (pagination) => ({\r\n  type: PromotionActions.SET_PROMOTION_PAGINATION,\r\n  payload: pagination\r\n});\r\n\r\nexport const resetPromotionFilters = () => ({\r\n  type: PromotionActions.RESET_PROMOTION_FILTERS\r\n});\r\n\r\n// Promotion User Management Action Creators\r\nexport const getPromotionUsers = (params) => ({\r\n  type: PromotionActions.GET_PROMOTION_USERS,\r\n  payload: params\r\n});\r\n\r\nexport const getPromotionUsersSuccess = (data) => ({\r\n  type: PromotionActions.GET_PROMOTION_USERS_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const getPromotionUsersFailure = (error) => ({\r\n  type: PromotionActions.GET_PROMOTION_USERS_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const getPromotionStats = (params) => ({\r\n  type: PromotionActions.GET_PROMOTION_STATS,\r\n  payload: params\r\n});\r\n\r\nexport const getPromotionStatsSuccess = (data) => ({\r\n  type: PromotionActions.GET_PROMOTION_STATS_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const getPromotionStatsFailure = (error) => ({\r\n  type: PromotionActions.GET_PROMOTION_STATS_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const getUserPromotions = (params) => ({\r\n  type: PromotionActions.GET_USER_PROMOTIONS,\r\n  payload: params\r\n});\r\n\r\nexport const getUserPromotionsSuccess = (data) => ({\r\n  type: PromotionActions.GET_USER_PROMOTIONS_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const getUserPromotionsFailure = (error) => ({\r\n  type: PromotionActions.GET_USER_PROMOTIONS_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const removeUserFromPromotion = (params) => ({\r\n  type: PromotionActions.REMOVE_USER_FROM_PROMOTION,\r\n  payload: params\r\n});\r\n\r\nexport const removeUserFromPromotionSuccess = (data) => ({\r\n  type: PromotionActions.REMOVE_USER_FROM_PROMOTION_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const removeUserFromPromotionFailure = (error) => ({\r\n  type: PromotionActions.REMOVE_USER_FROM_PROMOTION_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport const resetUserPromotionUsage = (params) => ({\r\n  type: PromotionActions.RESET_USER_PROMOTION_USAGE,\r\n  payload: params\r\n});\r\n\r\nexport const resetUserPromotionUsageSuccess = (data) => ({\r\n  type: PromotionActions.RESET_USER_PROMOTION_USAGE_SUCCESS,\r\n  payload: data\r\n});\r\n\r\nexport const resetUserPromotionUsageFailure = (error) => ({\r\n  type: PromotionActions.RESET_USER_PROMOTION_USAGE_FAILURE,\r\n  payload: error\r\n});\r\n\r\nexport default PromotionActions;\r\n"], "mappings": "AAAA,MAAMA,gBAAgB,GAAG;EACvB;EACAC,oBAAoB,EAAE,sBAAsB;EAC5CC,4BAA4B,EAAE,8BAA8B;EAC5DC,4BAA4B,EAAE,8BAA8B;EAE5D;EACAC,gBAAgB,EAAE,kBAAkB;EACpCC,wBAAwB,EAAE,0BAA0B;EACpDC,wBAAwB,EAAE,0BAA0B;EAEpD;EACAC,gBAAgB,EAAE,kBAAkB;EACpCC,wBAAwB,EAAE,0BAA0B;EACpDC,wBAAwB,EAAE,0BAA0B;EAEpD;EACAC,gBAAgB,EAAE,kBAAkB;EACpCC,wBAAwB,EAAE,0BAA0B;EACpDC,wBAAwB,EAAE,0BAA0B;EAEpD;EACAC,mBAAmB,EAAE,qBAAqB;EAC1CC,2BAA2B,EAAE,6BAA6B;EAC1DC,2BAA2B,EAAE,6BAA6B;EAE1D;EACAC,uBAAuB,EAAE,yBAAyB;EAClDC,+BAA+B,EAAE,iCAAiC;EAClEC,+BAA+B,EAAE,iCAAiC;EAElE;EACAC,qBAAqB,EAAE,uBAAuB;EAE9C;EACAC,qBAAqB,EAAE,uBAAuB;EAC9CC,wBAAwB,EAAE,0BAA0B;EACpDC,uBAAuB,EAAE,yBAAyB;EAElD;EACAC,mBAAmB,EAAE,qBAAqB;EAC1CC,2BAA2B,EAAE,6BAA6B;EAC1DC,2BAA2B,EAAE,6BAA6B;EAE1DC,mBAAmB,EAAE,qBAAqB;EAC1CC,2BAA2B,EAAE,6BAA6B;EAC1DC,2BAA2B,EAAE,6BAA6B;EAE1DC,mBAAmB,EAAE,qBAAqB;EAC1CC,2BAA2B,EAAE,6BAA6B;EAC1DC,2BAA2B,EAAE,6BAA6B;EAE1DC,0BAA0B,EAAE,4BAA4B;EACxDC,kCAAkC,EAAE,oCAAoC;EACxEC,kCAAkC,EAAE,oCAAoC;EAExEC,0BAA0B,EAAE,4BAA4B;EACxDC,kCAAkC,EAAE,oCAAoC;EACxEC,kCAAkC,EAAE,oCAAoC;EAExE;EACAC,2BAA2B,EAAE,6BAA6B;EAC1DC,mCAAmC,EAAE,qCAAqC;EAC1EC,mCAAmC,EAAE,qCAAqC;EAE1EC,yBAAyB,EAAE,2BAA2B;EACtDC,iCAAiC,EAAE,mCAAmC;EACtEC,iCAAiC,EAAE;AACrC,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAIC,MAAM,KAAM;EAC7CC,IAAI,EAAE9C,gBAAgB,CAACC,oBAAoB;EAC3C8C,OAAO,EAAEF;AACX,CAAC,CAAC;AAEF,OAAO,MAAMG,yBAAyB,GAAIC,IAAI,KAAM;EAClDH,IAAI,EAAE9C,gBAAgB,CAACE,4BAA4B;EACnD6C,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMC,yBAAyB,GAAIC,KAAK,KAAM;EACnDL,IAAI,EAAE9C,gBAAgB,CAACG,4BAA4B;EACnD4C,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMC,eAAe,GAAIH,IAAI,KAAM;EACxCH,IAAI,EAAE9C,gBAAgB,CAACI,gBAAgB;EACvC2C,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMI,sBAAsB,GAAIJ,IAAI,KAAM;EAC/CH,IAAI,EAAE9C,gBAAgB,CAACK,wBAAwB;EAC/C0C,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMK,sBAAsB,GAAIH,KAAK,KAAM;EAChDL,IAAI,EAAE9C,gBAAgB,CAACM,wBAAwB;EAC/CyC,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMI,eAAe,GAAIN,IAAI,KAAM;EACxCH,IAAI,EAAE9C,gBAAgB,CAACO,gBAAgB;EACvCwC,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMO,sBAAsB,GAAIP,IAAI,KAAM;EAC/CH,IAAI,EAAE9C,gBAAgB,CAACQ,wBAAwB;EAC/CuC,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMQ,sBAAsB,GAAIN,KAAK,KAAM;EAChDL,IAAI,EAAE9C,gBAAgB,CAACS,wBAAwB;EAC/CsC,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMO,eAAe,GAAIT,IAAI,KAAM;EACxCH,IAAI,EAAE9C,gBAAgB,CAACU,gBAAgB;EACvCqC,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMU,sBAAsB,GAAIV,IAAI,KAAM;EAC/CH,IAAI,EAAE9C,gBAAgB,CAACW,wBAAwB;EAC/CoC,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMW,sBAAsB,GAAIT,KAAK,KAAM;EAChDL,IAAI,EAAE9C,gBAAgB,CAACY,wBAAwB;EAC/CmC,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMU,gBAAgB,GAAIZ,IAAI,KAAM;EACzCH,IAAI,EAAE9C,gBAAgB,CAACa,mBAAmB;EAC1CkC,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMa,uBAAuB,GAAIb,IAAI,KAAM;EAChDH,IAAI,EAAE9C,gBAAgB,CAACc,2BAA2B;EAClDiC,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMc,uBAAuB,GAAIZ,KAAK,KAAM;EACjDL,IAAI,EAAE9C,gBAAgB,CAACe,2BAA2B;EAClDgC,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMa,qBAAqB,GAAIf,IAAI,KAAM;EAC9CH,IAAI,EAAE9C,gBAAgB,CAACgB,uBAAuB;EAC9C+B,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMgB,4BAA4B,GAAIhB,IAAI,KAAM;EACrDH,IAAI,EAAE9C,gBAAgB,CAACiB,+BAA+B;EACtD8B,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMiB,4BAA4B,GAAIf,KAAK,KAAM;EACtDL,IAAI,EAAE9C,gBAAgB,CAACkB,+BAA+B;EACtD6B,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMgB,mBAAmB,GAAGA,CAAA,MAAO;EACxCrB,IAAI,EAAE9C,gBAAgB,CAACmB;AACzB,CAAC,CAAC;AAEF,OAAO,MAAMiD,mBAAmB,GAAIC,OAAO,KAAM;EAC/CvB,IAAI,EAAE9C,gBAAgB,CAACoB,qBAAqB;EAC5C2B,OAAO,EAAEsB;AACX,CAAC,CAAC;AAEF,OAAO,MAAMC,sBAAsB,GAAIC,UAAU,KAAM;EACrDzB,IAAI,EAAE9C,gBAAgB,CAACqB,wBAAwB;EAC/C0B,OAAO,EAAEwB;AACX,CAAC,CAAC;AAEF,OAAO,MAAMC,qBAAqB,GAAGA,CAAA,MAAO;EAC1C1B,IAAI,EAAE9C,gBAAgB,CAACsB;AACzB,CAAC,CAAC;;AAEF;AACA,OAAO,MAAMmD,iBAAiB,GAAI5B,MAAM,KAAM;EAC5CC,IAAI,EAAE9C,gBAAgB,CAACuB,mBAAmB;EAC1CwB,OAAO,EAAEF;AACX,CAAC,CAAC;AAEF,OAAO,MAAM6B,wBAAwB,GAAIzB,IAAI,KAAM;EACjDH,IAAI,EAAE9C,gBAAgB,CAACwB,2BAA2B;EAClDuB,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAM0B,wBAAwB,GAAIxB,KAAK,KAAM;EAClDL,IAAI,EAAE9C,gBAAgB,CAACyB,2BAA2B;EAClDsB,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMyB,iBAAiB,GAAI/B,MAAM,KAAM;EAC5CC,IAAI,EAAE9C,gBAAgB,CAAC0B,mBAAmB;EAC1CqB,OAAO,EAAEF;AACX,CAAC,CAAC;AAEF,OAAO,MAAMgC,wBAAwB,GAAI5B,IAAI,KAAM;EACjDH,IAAI,EAAE9C,gBAAgB,CAAC2B,2BAA2B;EAClDoB,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAM6B,wBAAwB,GAAI3B,KAAK,KAAM;EAClDL,IAAI,EAAE9C,gBAAgB,CAAC4B,2BAA2B;EAClDmB,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAM4B,iBAAiB,GAAIlC,MAAM,KAAM;EAC5CC,IAAI,EAAE9C,gBAAgB,CAAC6B,mBAAmB;EAC1CkB,OAAO,EAAEF;AACX,CAAC,CAAC;AAEF,OAAO,MAAMmC,wBAAwB,GAAI/B,IAAI,KAAM;EACjDH,IAAI,EAAE9C,gBAAgB,CAAC8B,2BAA2B;EAClDiB,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMgC,wBAAwB,GAAI9B,KAAK,KAAM;EAClDL,IAAI,EAAE9C,gBAAgB,CAAC+B,2BAA2B;EAClDgB,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAM+B,uBAAuB,GAAIrC,MAAM,KAAM;EAClDC,IAAI,EAAE9C,gBAAgB,CAACgC,0BAA0B;EACjDe,OAAO,EAAEF;AACX,CAAC,CAAC;AAEF,OAAO,MAAMsC,8BAA8B,GAAIlC,IAAI,KAAM;EACvDH,IAAI,EAAE9C,gBAAgB,CAACiC,kCAAkC;EACzDc,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMmC,8BAA8B,GAAIjC,KAAK,KAAM;EACxDL,IAAI,EAAE9C,gBAAgB,CAACkC,kCAAkC;EACzDa,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,OAAO,MAAMkC,uBAAuB,GAAIxC,MAAM,KAAM;EAClDC,IAAI,EAAE9C,gBAAgB,CAACmC,0BAA0B;EACjDY,OAAO,EAAEF;AACX,CAAC,CAAC;AAEF,OAAO,MAAMyC,8BAA8B,GAAIrC,IAAI,KAAM;EACvDH,IAAI,EAAE9C,gBAAgB,CAACoC,kCAAkC;EACzDW,OAAO,EAAEE;AACX,CAAC,CAAC;AAEF,OAAO,MAAMsC,8BAA8B,GAAIpC,KAAK,KAAM;EACxDL,IAAI,EAAE9C,gBAAgB,CAACqC,kCAAkC;EACzDU,OAAO,EAAEI;AACX,CAAC,CAAC;AAEF,eAAenD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}