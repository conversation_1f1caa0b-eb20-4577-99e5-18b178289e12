.modal-header {
    border-bottom: 1px solid #dee2e6;
    position: relative;
  }
  
  .close-button {
    position: absolute;
    right: 15px;
    top: 15px;
    padding: 0;
    font-size: 1.5rem;
    color: #000;
    opacity: 0.5;
  }
  
  .close-button:hover {
    opacity: 0.75;
  }
  
  .reservation-section,
  .cancellation-section {
    margin-bottom: 20px;
  }
  
  .reservation-details {
    margin-top: 15px;
  }
  
  .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  
  .status-badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    text-transform: uppercase;
  }
  
  .refund-alert {
    background-color: #d1e7dd;
    color: #0f5132;
    padding: 15px;
    border-radius: 4px;
    text-align: center;
  }
  
  .disclaimer {
    font-size: 0.875rem;
    color: #6c757d;
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
  }
  
  .confirm-button {
    background-color: #dc3545;
    border-color: #dc3545;
  }
  
  