.payments-content {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.content-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.filters-bar {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.search-box {
  position: relative;
  width: 300px;
  min-width: 250px;
}

.search-box i {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-box input {
  width: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.search-box input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filters {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.filters .form-select {
  min-width: 150px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.filters .form-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.table-responsive {
  overflow-x: auto;
}

.table {
  margin: 0;
}

.table th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 15px 12px;
  font-size: 14px;
}

.table td {
  padding: 12px;
  vertical-align: middle;
  border-bottom: 1px solid #dee2e6;
  font-size: 14px;
}

.table tbody tr:hover {
  background-color: #f8f9fa;
}

.badge {
  font-size: 12px;
  padding: 6px 10px;
  border-radius: 20px;
}

.action-buttons {
  display: flex;
  gap: 5px;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.action-buttons .btn {
  padding: 6px 10px;
  font-size: 12px;
  border-radius: 4px;
}

.action-buttons .btn i {
  font-size: 14px;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

.pagination-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.summary-info {
  text-align: right;
}

.pagination-info {
  color: #6c757d;
  font-size: 14px;
}

.pagination {
  margin: 0;
  display: flex;
  gap: 5px;
  align-items: center;
}

.pagination .page-item .page-link {
  border: 1px solid #dee2e6;
  color: #007bff;
  padding: 8px 12px;
  border-radius: 4px;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination .page-item .page-link:hover {
  background-color: #e9ecef;
  color: #0056b3;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
}

.pagination .page-item.active .page-link:hover {
  background-color: #0056b3;
  border-color: #0056b3;
  color: white;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
}

.pagination .page-item.disabled .page-link:hover {
  background-color: #fff;
  color: #6c757d;
}

/* Modal styles */
.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  font-weight: 600;
  color: #333;
}

.modal-body h6 {
  color: #495057;
  font-weight: 600;
  margin-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 5px;
}

.modal-body p {
  margin-bottom: 8px;
  font-size: 14px;
}

.modal-body strong {
  color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
  .payments-content {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .filters-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .filters {
    justify-content: center;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 3px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 15px;
  }
  
  .pagination-right {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }
  
  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .pagination .page-item .page-link {
    padding: 6px 10px;
    font-size: 14px;
  }
  
  .search-box {
    width: 100%;
    min-width: auto;
  }
} 

.table th.action-col, .table td.action-col {
  text-align: center;
  vertical-align: middle;
  width: 160px;
  min-width: 140px;
  max-width: 180px;
  padding-left: 0;
  padding-right: 0;
} 