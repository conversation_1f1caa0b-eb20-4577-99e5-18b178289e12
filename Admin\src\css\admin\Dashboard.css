/* Base Styles */
:root {
  --primary-color: #4361ee;
  --primary-hover: #3a56d4;
  --secondary-color: #f72585;
  --success-color: #20c997;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #0dcaf0;
  --dark-color: #212529;
  --light-color: #f8f9fa;
  --gray-color: #6c757d;
  --gray-light-color: #e9ecef;
  --sidebar-width: 260px;
  --sidebar-collapsed-width: 70px;
  --header-height: 60px;
  --border-radius: 8px;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --transition-speed: 0.3s;
}

body {
  margin: 0;
  font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f8f9fa;
  overflow-x: hidden;
}

/* Admin Dashboard Layout */
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* Sidebar Styles */
.sidebar {
  width: var(--sidebar-width);
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: var(--dark-color);
  color: white;
  z-index: 1000;
  transition: width var(--transition-speed) ease;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.sidebar-logo i {
  margin-right: 10px;
  font-size: 1.8rem;
}

.sidebar.collapsed .logo-text {
  display: none;
}

.sidebar-toggle {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px 0;
}

.sidebar-user {
  display: flex;
  align-items: center;
  padding: 0 15px 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
}

.sidebar.collapsed .sidebar-user {
  justify-content: center;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 10px;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.sidebar.collapsed .user-info {
  display: none;
}

.user-name {
  font-weight: 600;
  margin-bottom: 2px;
  font-size: 0.9rem;
}

.user-role {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

.menu-category {
  padding: 10px 15px;
  font-size: 0.75rem;
  text-transform: uppercase;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 5px;
  letter-spacing: 1px;
}

.sidebar.collapsed .menu-category {
  text-align: center;
  font-size: 0.6rem;
}

.menu-items {
  list-style: none;
  padding: 0;
  margin: 0 0 20px;
}

.menu-item {
  margin-bottom: 2px;
}

.menu-item a {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 5px;
  margin: 0 5px;
  transition: all 0.3s ease;
  position: relative;
}

.menu-item a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.menu-item.active a {
  background-color: var(--primary-color);
  color: white;
}

.menu-item i {
  margin-right: 10px;
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.sidebar.collapsed .menu-item span {
  display: none;
}

.menu-item .badge {
  position: absolute;
  right: 10px;
  font-size: 0.7rem;
  padding: 3px 6px;
}

.sidebar.collapsed .menu-item .badge {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 0.6rem;
  padding: 2px 4px;
}

/* Main Content Styles */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left var(--transition-speed) ease;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed ~ .main-content {
  margin-left: var(--sidebar-collapsed-width);
}

/* Header Styles */
.header {
  height: var(--header-height);
  background-color: white;
  border-bottom: 1px solid var(--gray-light-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: var(--box-shadow);
}

.header-left {
  display: flex;
  align-items: center;
}

.search-box {
  position: relative;
  width: 300px;
}

.search-box i {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-color);
}

.search-box input {
  width: 100%;
  padding: 8px 10px 8px 35px;
  border: 1px solid var(--gray-light-color);
  border-radius: 20px;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Notification Styles */
.notification-container {
  position: relative;
}

.notification-button {
  background: none;
  border: none;
  color: var(--gray-color);
  font-size: 1.2rem;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: relative;
}

.notification-button:hover {
  background-color: #f8f9fa;
  color: var(--primary-color);
}

.notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--danger-color);
  color: white;
  font-size: 0.7rem;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
  z-index: 1000;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--gray-light-color);
}

.notification-header h6 {
  margin: 0;
  font-weight: 600;
}

.mark-all-read {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.8rem;
  cursor: pointer;
  padding: 0;
}

.notification-body {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  padding: 12px 15px;
  border-bottom: 1px solid var(--gray-light-color);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notification-item:hover {
  background-color: #f8f9fa;
}

.notification-item.unread {
  background-color: rgba(67, 97, 238, 0.05);
}

.notification-icon-wrapper {
  margin-right: 12px;
  display: flex;
  align-items: flex-start;
}

.notification-icon {
  font-size: 1.2rem;
  padding: 8px;
  border-radius: 50%;
  background-color: #f1f1f1;
  color: var(--gray-color);
}

.notification-icon.approval {
  background-color: rgba(32, 201, 151, 0.1);
  color: var(--success-color);
}

.notification-icon.report {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
}

.notification-icon.payment {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning-color);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 3px;
}

.notification-message {
  font-size: 0.85rem;
  color: var(--gray-color);
  margin-bottom: 5px;
}

.notification-time {
  font-size: 0.75rem;
  color: #adb5bd;
}

.unread-indicator {
  width: 8px;
  height: 8px;
  background-color: var(--primary-color);
  border-radius: 50%;
  position: absolute;
  top: 15px;
  right: 15px;
}

.notification-footer {
  padding: 10px;
  text-align: center;
  border-top: 1px solid var(--gray-light-color);
}

.notification-footer button {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.85rem;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.notification-footer button:hover {
  background-color: rgba(67, 97, 238, 0.1);
}

/* User Menu Styles */
.user-menu-container {
  position: relative;
}

.user-menu-button {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-menu-button:hover {
  background-color: #f8f9fa;
}

.user-menu-button .user-info {
  margin: 0 10px;
  text-align: left;
}

.user-menu-button .user-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--dark-color);
}

.user-menu-button .user-role {
  font-size: 0.75rem;
  color: var(--gray-color);
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-top: 10px;
  z-index: 1000;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}

.user-dropdown ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.user-dropdown li {
  border-bottom: 1px solid var(--gray-light-color);
}

.user-dropdown li:last-child {
  border-bottom: none;
}

.user-dropdown a {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: var(--dark-color);
  text-decoration: none;
  transition: all 0.2s ease;
}

.user-dropdown a:hover {
  background-color: #f8f9fa;
}

.user-dropdown a i {
  margin-right: 10px;
  font-size: 1.1rem;
  width: 20px;
  text-align: center;
}

.user-dropdown .divider {
  height: 1px;
  background-color: var(--gray-light-color);
  margin: 5px 0;
}

.user-dropdown .logout {
  color: var(--danger-color);
}

/* Page Content Styles */
.page-content {
  flex: 1;
  padding: 20px;
  background-color: #f8f9fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 10px;
}

.date-filter {
  width: 150px;
}

/* Stats Cards */
.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: var(--box-shadow);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.stat-card-content h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 5px;
}

.stat-card-content p {
  margin: 0;
  color: var(--gray-color);
  font-size: 0.9rem;
}

.stat-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
}

.stat-card-icon.hotels {
  background-color: rgba(67, 97, 238, 0.1);
  color: var(--primary-color);
}

.stat-card-icon.active {
  background-color: rgba(32, 201, 151, 0.1);
  color: var(--success-color);
}

.stat-card-icon.pending {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning-color);
}

.stat-card-icon.customers {
  background-color: rgba(247, 37, 133, 0.1);
  color: var(--secondary-color);
}

.stat-card-icon.owners {
  background-color: rgba(13, 202, 240, 0.1);
  color: var(--info-color);
}

/* Chart Container */
.chart-container {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 20px;
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--gray-light-color);
}

.chart-header h2 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.chart-body {
  padding: 20px;
  height: 350px;
}

.charts-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.chart-container.half .chart-body {
  height: 300px;
}

/* Recent Activities */
.recent-activities {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.activity-container {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--gray-light-color);
}

.activity-header h2 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.view-all {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.view-all:hover {
  text-decoration: underline;
}

.activity-body {
  padding: 0;
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
}

.table {
  width: 100%;
  margin-bottom: 0;
  color: var(--dark-color);
  vertical-align: middle;
  border-color: var(--gray-light-color);
}

.table th {
  font-weight: 600;
  font-size: 0.85rem;
  padding: 12px 15px;
  border-bottom-width: 1px;
  white-space: nowrap;
}

.table td {
  padding: 12px 15px;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: rgba(67, 97, 238, 0.05);
}

.badge {
  font-weight: 500;
  padding: 5px 10px;
  border-radius: 20px;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.action-buttons .btn {
  width: 30px;
  height: 30px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

/* Content Container */
.content-container {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 20px;
  overflow: hidden;
}

.filters-bar {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--gray-light-color);
  flex-wrap: wrap;
  gap: 10px;
}

.filters {
  display: flex;
  gap: 10px;
  margin-left: auto;
  flex-wrap: wrap;
}

.date-range {
  display: flex;
  gap: 10px;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-top: 1px solid var(--gray-light-color);
}

.pagination-info {
  color: var(--gray-color);
  font-size: 0.9rem;
}

.pagination {
  margin: 0;
}

.page-link {
  color: var(--primary-color);
  border-color: var(--gray-light-color);
}

.page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .stats-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .charts-row,
  .recent-activities {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 992px) {
  .sidebar {
    width: var(--sidebar-collapsed-width);
  }
  
  .sidebar .logo-text,
  .sidebar .user-info,
  .sidebar .menu-item span,
  .sidebar .menu-item .badge {
    display: none;
  }
  
  .main-content {
    margin-left: var(--sidebar-collapsed-width);
  }
  
  .sidebar.collapsed {
    width: 0;
    overflow: hidden;
  }
  
  .sidebar.collapsed ~ .main-content {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .stats-cards {
    grid-template-columns: 1fr;
  }
  
  .search-box {
    width: 200px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .page-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .filters-bar {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filters {
    width: 100%;
    margin-left: 0;
    margin-top: 10px;
  }
  
  .search-box {
    width: 100%;
  }
  
  .notification-dropdown,
  .user-dropdown {
    position: fixed;
    width: 100%;
    left: 0;
    right: 0;
    top: var(--header-height);
    margin-top: 0;
    border-radius: 0;
  }
}
