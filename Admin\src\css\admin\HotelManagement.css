/* Sidebar styles */
.sidebar {
    background-color: #1a2b49;
    height: 100vh;
    position: sticky;
    top: 0;
    padding: 0;
    color: white;
  }
  
  .sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .sidebar-link {
    color: rgba(255, 255, 255, 0.8) !important;
    padding: 0.75rem 1.5rem;
    transition: all 0.2s;
  }
  
  .sidebar-link:hover,
  .sidebar-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white !important;
  }
  
  .sidebar-link.active {
    border-left: 4px solid #0d6efd;
  }
  
  /* Tab styles */
  .hotel-host-tabs .nav-link {
    color: #495057;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    border: none;
    border-bottom: 2px solid transparent;
  }
  
  .hotel-host-tabs .nav-link.active {
    color: #0d6efd;
    background-color: transparent;
    border-bottom: 2px solid #0d6efd;
  }
  
  .hotel-host-tabs .nav-link:hover:not(.active) {
    border-bottom: 2px solid #dee2e6;
  }
  
  /* Hotel images grid */
  .hotel-images-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .hotel-image-item {
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .hotel-image-item:hover {
    opacity: 0.9;
    transform: scale(1.02);
  }
  
  /* Stats cards */
  .stat-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    min-width: 100px;
    text-align: center;
  }
  
  .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1a2b49;
  }
  
  .stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* Amenities */
  .amenities-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .amenity-badge {
    padding: 0.5rem 1rem;
    font-weight: normal;
    border: 1px solid #dee2e6;
  }
  
  /* Feedback cards */
  .feedback-card {
    transition: all 0.2s;
  }
  
  .feedback-card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }
  
  /* Transaction table */
  .transaction-table th {
    background-color: #f8f9fa;
    font-weight: 600;
  }
  
  .transaction-summary {
    background-color: #f8f9fa;
  }
  
  .transaction-stat {
    text-align: center;
  }
  
  .transaction-stat .stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
  }
  
  .transaction-stat .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .hotel-images-grid {
      grid-template-columns: 1fr;
    }
  
    .transaction-stat {
      margin-bottom: 1rem;
    }
  }
  
  