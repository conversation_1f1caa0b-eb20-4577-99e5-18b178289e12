import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  Modal,
  Table,
  Button,
  Form,
  InputGroup,
  Badge,
  Spinner,
  <PERSON>ert,
  Row,
  Col,
  Pagination,
  OverlayTrigger,
  Tooltip,
} from "react-bootstrap";
import {
  FaSearch,
  FaUser,
  FaCheck,
  FaTimes,
  FaUserPlus,
  FaUsers,
} from "react-icons/fa";
import {
  searchUsersForAssignment,
  assignPromotionToUsers,
} from "../../redux/promotion/actions";

const AssignPromotionModal = ({ show, onHide, promotion, onAssignSuccess }) => {
  const dispatch = useDispatch();
  const { assignUsers, assigningUsers } = useSelector(
    (state) => state.Promotion
  );

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUserIds, setSelectedUserIds] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    if (show && promotion?._id) {
      loadUsers();
    }
  }, [show, promotion, searchTerm, currentPage]);

  useEffect(() => {
    // Reset state when modal opens
    if (show) {
      setSearchTerm("");
      setSelectedUserIds([]);
      setCurrentPage(1);
    }
  }, [show]);

  const loadUsers = () => {
    const params = {
      page: currentPage,
      limit: 10,
      search: searchTerm,
    };

    dispatch(
      searchUsersForAssignment({
        promotionId: promotion._id,
        params,
        onSuccess: (data) => {
          console.log("✅ Users loaded for assignment:", data);
        },
        onFailed: (error) => {
          console.error("❌ Failed to load users:", error);
        },
      })
    );
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleUserSelect = (userId) => {
    setSelectedUserIds((prev) => {
      if (prev.includes(userId)) {
        return prev.filter((id) => id !== userId);
      } else {
        return [...prev, userId];
      }
    });
  };

  const handleSelectAll = () => {
    const availableUserIds = assignUsers.availableUsers
      .filter((user) => !user.isAssigned)
      .map((user) => user._id);

    if (selectedUserIds.length === availableUserIds.length) {
      setSelectedUserIds([]);
    } else {
      setSelectedUserIds(availableUserIds);
    }
  };

  const handleAssignUsers = () => {
    if (selectedUserIds.length === 0) {
      return;
    }

    dispatch(
      assignPromotionToUsers({
        promotionId: promotion._id,
        userIds: selectedUserIds,
        onSuccess: (data) => {
          console.log("✅ Users assigned successfully:", data);
          setSelectedUserIds([]);
          onAssignSuccess && onAssignSuccess(data);
          // Reload users to update assignment status
          loadUsers();
        },
        onFailed: (error) => {
          console.error("❌ Failed to assign users:", error);
        },
      })
    );
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const formatDate = (date) => {
    if (!date) return "N/A";
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const availableUsers = assignUsers.availableUsers.filter(
    (user) => !user.isAssigned
  );
  const assignedUsers = assignUsers.availableUsers.filter(
    (user) => user.isAssigned
  );

  return (
    <Modal show={show} onHide={onHide} size="xl" centered>
      <Modal.Header closeButton>
        <Modal.Title>
          <FaUserPlus className="me-2" />
          Assign Users to Promotion: {promotion?.code}
        </Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {promotion?.type !== "PRIVATE" && (
          <Alert variant="warning">
            <strong>Note:</strong> Only private promotions can be assigned to
            specific users. This promotion is {promotion?.type}.
          </Alert>
        )}

        {promotion?.type === "PRIVATE" && (
          <>
            {/* Search */}
            <Row className="mb-3">
              <Col md={8}>
                <InputGroup>
                  <InputGroup.Text>
                    <FaSearch />
                  </InputGroup.Text>
                  <Form.Control
                    type="text"
                    placeholder="Search users by name, email, or phone..."
                    value={searchTerm}
                    onChange={handleSearchChange}
                  />
                </InputGroup>
              </Col>
              <Col md={4}>
                <div className="d-flex justify-content-end align-items-center">
                  <span className="text-muted me-3">
                    {selectedUserIds.length} selected
                  </span>
                  <Button
                    variant="primary"
                    onClick={handleAssignUsers}
                    disabled={selectedUserIds.length === 0 || assigningUsers}
                  >
                    {assigningUsers ? (
                      <>
                        <Spinner animation="border" size="sm" className="me-2" />
                        Assigning...
                      </>
                    ) : (
                      <>
                        <FaUserPlus className="me-2" />
                        Assign Selected ({selectedUserIds.length})
                      </>
                    )}
                  </Button>
                </div>
              </Col>
            </Row>

            {/* Users Table */}
            {assignUsers.loading ? (
              <div className="text-center py-4">
                <Spinner animation="border" />
                <p className="mt-2">Loading users...</p>
              </div>
            ) : assignUsers.error ? (
              <Alert variant="danger">{assignUsers.error}</Alert>
            ) : (
              <>
                <Table responsive striped hover>
                  <thead>
                    <tr>
                      <th>
                        <Form.Check
                          type="checkbox"
                          checked={
                            availableUsers.length > 0 &&
                            selectedUserIds.length === availableUsers.length
                          }
                          onChange={handleSelectAll}
                          disabled={availableUsers.length === 0}
                        />
                      </th>
                      <th>User</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Joined</th>
                      <th>Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {assignUsers.availableUsers.map((user) => (
                      <tr key={user._id}>
                        <td>
                          <Form.Check
                            type="checkbox"
                            checked={selectedUserIds.includes(user._id)}
                            onChange={() => handleUserSelect(user._id)}
                            disabled={user.isAssigned}
                          />
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            {user.avatar && (
                              <img
                                src={user.avatar}
                                alt={user.name}
                                className="rounded-circle me-2"
                                style={{ width: "32px", height: "32px" }}
                              />
                            )}
                            <div>
                              <strong>{user.name}</strong>
                            </div>
                          </div>
                        </td>
                        <td>{user.email}</td>
                        <td>{user.phoneNumber || "N/A"}</td>
                        <td>{formatDate(user.createdAt)}</td>
                        <td>
                          {user.isAssigned ? (
                            <Badge bg="success">
                              <FaCheck className="me-1" />
                              Assigned
                            </Badge>
                          ) : (
                            <Badge bg="secondary">
                              <FaUser className="me-1" />
                              Available
                            </Badge>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>

                {assignUsers.availableUsers.length === 0 && (
                  <div className="text-center py-4">
                    <FaUsers size={48} className="text-muted mb-3" />
                    <h5 className="text-muted">No users found</h5>
                    <p className="text-muted">
                      Try adjusting your search criteria
                    </p>
                  </div>
                )}

                {/* Pagination */}
                {assignUsers.pagination.totalPages > 1 && (
                  <div className="d-flex justify-content-center mt-3">
                    <Pagination>
                      <Pagination.Prev
                        disabled={!assignUsers.pagination.hasPrevPage}
                        onClick={() => handlePageChange(currentPage - 1)}
                      />
                      {[...Array(assignUsers.pagination.totalPages)].map(
                        (_, index) => (
                          <Pagination.Item
                            key={index + 1}
                            active={index + 1 === currentPage}
                            onClick={() => handlePageChange(index + 1)}
                          >
                            {index + 1}
                          </Pagination.Item>
                        )
                      )}
                      <Pagination.Next
                        disabled={!assignUsers.pagination.hasNextPage}
                        onClick={() => handlePageChange(currentPage + 1)}
                      />
                    </Pagination>
                  </div>
                )}

                {/* Summary */}
                <div className="mt-3 p-3 bg-light rounded">
                  <Row>
                    <Col md={6}>
                      <strong>Available Users:</strong> {availableUsers.length}
                    </Col>
                    <Col md={6}>
                      <strong>Already Assigned:</strong> {assignedUsers.length}
                    </Col>
                  </Row>
                </div>
              </>
            )}
          </>
        )}
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onHide}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AssignPromotionModal;
