{"ast": null, "code": "const ApiConstants = {\n  //AUTH:\n  LOGIN_CUSTOMER: \"/auth/login_customer\",\n  GOOGLE_LOGIN: \"./auth/google_login\",\n  REGISTER_CUSTOMER: \"/auth/register_customer\",\n  UPDATE_PROFILE: \"/auth/updateProfile_customer\",\n  CHANGE_PASSWORD: \"/auth/changePassword_customer\",\n  VERIFY_EMAIL: \"/auth/verify-email\",\n  RESEND_VERIFICATION: \"/auth/resend-verification\",\n  UPDATE_PROFILE: \"/auth/updateProfile_customer\",\n  CHANGE_PASSWORD: \"/auth/changePassword_customer\",\n  UPDATE_AVATAR: \"/auth/update_avatar\",\n  FORGOT_PASSWORD: \"/auth/forgot_password\",\n  RESET_PASSWORD: \"/auth/reset_password\",\n  VERIFY_FORGOT_PASSWORD: \"/auth/verify_forgot_password\",\n  //SEARCH:\n  SEARCH_HOTEL: \"/search\",\n  FETCH_FAVORITE_HOTELS: \"/hotel/get-hotel-byId\",\n  REMOVE_FAVORITE_HOTELS: \"/hotel/remove-favorite\",\n  ADD_FAVORITE_HOTELS: \"/hotel/add-favorite\",\n  //HOTEL\n  FETCH_DETAIL_HOTELS: \"/hotel/hotel_detail/:hotelId\",\n  FETCH_ROOM: \"/room/rooms_information/:hotelId\",\n  FETCH_ALL_HOTEL: \"/hotel/get-all-hotel\",\n  //FEEDBACK:\n  FETCH_ROOM_DETAIL: \"/room/rooms_detail/:roomId\",\n  FETCH_TOP3_HOTEL: \"hotel/top-bookings\",\n  //FEEDBACK:\n  FETCH_FEEDBACK_BY_HOTELID: 'feedback/get-feedback-hotel/:hotelId',\n  LIKE_FEEDBACK: 'feedback/like',\n  DISLIKE_FEEDBACK: 'feedback/dislike',\n  FETCH_FEEDBACK_BY_ID: 'feedback/getFeedbackById/:feedbackId',\n  //CREATE_BOOKING:\n  CREATE_BOOKING: 'payment/create-booking',\n  CHECKOUT_BOOKING: 'payment/checkout-booking',\n  CANCEL_PAYMENT: 'payment/cancel-payment',\n  ACCEPT_PAYMENT: 'payment/accept-payment',\n  //RESERVATION:\n  FETCH_DETAIL_RESERVATION: '/reservations/detail',\n  FETCH_USER_RESERVATIONS: \"reservations/get-reservation\",\n  FETCH_RESERVATION_DETAIL: \"reservations/reservations-detail/:id\",\n  UPDATE_RESERVATION: \"reservations/update-reservations/:id\",\n  //FEEDBACK\n  FETCH_FEEDBACK_BY_HOTELID: \"feedback/get-feedback-hotel/:hotelId\",\n  FETCH_USER_FEEDBACKS: \"feedback/my-feedbacks\",\n  UPDATE_FEEDBACK: \"feedback/update-feedback/:feedbackId\",\n  DELETE_FEEDBACK: \"\",\n  CREATE_FEEDBACK: \"feedback/create-feedback\",\n  UPDATE_FEEDBACK_STATUS: \"feedback/updateStatusFeedback/:feedbackId\",\n  //REFUNDING_RESERVATION\n  CREATE_REFUNDING_RESERVATION: \"refunding_reservation/create\",\n  GET_REFUNDING_RESERVATION_BYUSERID: \"refunding_reservation/by_userId\",\n  UPDATE_BANKING_INFO: \"refunding_reservation/banking-info\",\n  ///REPORTFEEDBACK\n  REPORT_FEEDBACK: \"reportFeedback/create_report_feedback\",\n  FETCH_REPORTS_BY_USERID: \"reportFeedback/my-reports\",\n  DELETE_REPORTED_FEEDBACK: \"reportFeedback/delete_report_feedback/:reportId\",\n  GET_ALL_REPORTED_FEEDBACKS: \"/reportFeedback/getReportedFeedbackDetails\",\n  GET_REPORTS_BY_FEEDBACK_ID: \"/reportFeedback/getReportedFeedbackByFeedbackId/:feedbackId\",\n  UPDATE_REPORT_STATUS: \"/reportFeedback/updateReportStatus/:reportId\",\n  //chat\n  FETCH_CHAT_MESSAGE: '/chat/chat-history/:receiverId',\n  FETCH_CHAT_ALL_USERS: '/chat/chat-users',\n  //ADMIN DASHBOARD\n  ADMIN_DASHBOARD_METRICS: '/dashboard-admin/metrics',\n  //MONTHLY PAYMENT (Admin)\n  FETCH_ALL_MONTHLY_PAYMENTS: \"/monthly-payment/admin/all\",\n  UPDATE_PAYMENT_STATUS: \"/monthly-payment/admin/:paymentId/status\",\n  GET_PAYMENT_BY_ID: \"/monthly-payment/admin/:paymentId\",\n  //PROMOTION (Admin)\n  FETCH_ALL_PROMOTIONS: \"/promotions\",\n  CREATE_PROMOTION: \"/promotions\",\n  UPDATE_PROMOTION: \"/promotions/:id\",\n  DELETE_PROMOTION: \"/promotions/:id\",\n  GET_PROMOTION_BY_ID: \"/promotions/:id\",\n  TOGGLE_PROMOTION_STATUS: \"/promotions/:id/status\",\n  // PROMOTION USER MANAGEMENT (Admin)\n  GET_PROMOTION_USERS: \"/promotions/:id/users\",\n  GET_PROMOTION_STATS: \"/promotions/:id/stats\",\n  GET_USER_PROMOTIONS: \"/promotions/users/:userId\",\n  REMOVE_USER_FROM_PROMOTION: \"/promotions/:id/users/:userId\",\n  RESET_USER_PROMOTION_USAGE: \"/promotions/:id/users/:userId/reset\",\n  GET_ALL_REFUND: \"/payment/getAllRefund\",\n  REFUND: \"payment/stripe-refund/:id\",\n  // CUSTOMER MANAGEMENT (Admin)\n  LOCK_CUSTOMER: \"/auth/lock-customer/:id\",\n  UNLOCK_CUSTOMER: \"/auth/unlock-customer/:id\"\n};\nexport default ApiConstants;", "map": {"version": 3, "names": ["ApiConstants", "LOGIN_CUSTOMER", "GOOGLE_LOGIN", "REGISTER_CUSTOMER", "UPDATE_PROFILE", "CHANGE_PASSWORD", "VERIFY_EMAIL", "RESEND_VERIFICATION", "UPDATE_AVATAR", "FORGOT_PASSWORD", "RESET_PASSWORD", "VERIFY_FORGOT_PASSWORD", "SEARCH_HOTEL", "FETCH_FAVORITE_HOTELS", "REMOVE_FAVORITE_HOTELS", "ADD_FAVORITE_HOTELS", "FETCH_DETAIL_HOTELS", "FETCH_ROOM", "FETCH_ALL_HOTEL", "FETCH_ROOM_DETAIL", "FETCH_TOP3_HOTEL", "FETCH_FEEDBACK_BY_HOTELID", "LIKE_FEEDBACK", "DISLIKE_FEEDBACK", "FETCH_FEEDBACK_BY_ID", "CREATE_BOOKING", "CHECKOUT_BOOKING", "CANCEL_PAYMENT", "ACCEPT_PAYMENT", "FETCH_DETAIL_RESERVATION", "FETCH_USER_RESERVATIONS", "FETCH_RESERVATION_DETAIL", "UPDATE_RESERVATION", "FETCH_USER_FEEDBACKS", "UPDATE_FEEDBACK", "DELETE_FEEDBACK", "CREATE_FEEDBACK", "UPDATE_FEEDBACK_STATUS", "CREATE_REFUNDING_RESERVATION", "GET_REFUNDING_RESERVATION_BYUSERID", "UPDATE_BANKING_INFO", "REPORT_FEEDBACK", "FETCH_REPORTS_BY_USERID", "DELETE_REPORTED_FEEDBACK", "GET_ALL_REPORTED_FEEDBACKS", "GET_REPORTS_BY_FEEDBACK_ID", "UPDATE_REPORT_STATUS", "FETCH_CHAT_MESSAGE", "FETCH_CHAT_ALL_USERS", "ADMIN_DASHBOARD_METRICS", "FETCH_ALL_MONTHLY_PAYMENTS", "UPDATE_PAYMENT_STATUS", "GET_PAYMENT_BY_ID", "FETCH_ALL_PROMOTIONS", "CREATE_PROMOTION", "UPDATE_PROMOTION", "DELETE_PROMOTION", "GET_PROMOTION_BY_ID", "TOGGLE_PROMOTION_STATUS", "GET_PROMOTION_USERS", "GET_PROMOTION_STATS", "GET_USER_PROMOTIONS", "REMOVE_USER_FROM_PROMOTION", "RESET_USER_PROMOTION_USAGE", "GET_ALL_REFUND", "REFUND", "LOCK_CUSTOMER", "UNLOCK_CUSTOMER"], "sources": ["E:/Uroom/Admin/src/adapter/ApiConstants.js"], "sourcesContent": ["const ApiConstants = {\r\n  //AUTH:\r\n  LOGIN_CUSTOMER: \"/auth/login_customer\",\r\n  GOOGLE_LOGIN: \"./auth/google_login\",\r\n  REGISTER_CUSTOMER: \"/auth/register_customer\",\r\n  UPDATE_PROFILE: \"/auth/updateProfile_customer\",\r\n  CHANGE_PASSWORD: \"/auth/changePassword_customer\",\r\n  VERIFY_EMAIL: \"/auth/verify-email\",\r\n  RESEND_VERIFICATION: \"/auth/resend-verification\",\r\n  UPDATE_PROFILE: \"/auth/updateProfile_customer\",\r\n  CHANGE_PASSWORD: \"/auth/changePassword_customer\",\r\n  UPDATE_AVATAR: \"/auth/update_avatar\",\r\n  FORGOT_PASSWORD: \"/auth/forgot_password\",\r\n  RESET_PASSWORD: \"/auth/reset_password\",\r\n  VERIFY_FORGOT_PASSWORD: \"/auth/verify_forgot_password\",\r\n  //SEARCH:\r\n  SEARCH_HOTEL: \"/search\",\r\n  FETCH_FAVORITE_HOTELS: \"/hotel/get-hotel-byId\",\r\n  REMOVE_FAVORITE_HOTELS: \"/hotel/remove-favorite\",\r\n  ADD_FAVORITE_HOTELS: \"/hotel/add-favorite\",\r\n  //HOTEL\r\n  FETCH_DETAIL_HOTELS: \"/hotel/hotel_detail/:hotelId\",\r\n  FETCH_ROOM: \"/room/rooms_information/:hotelId\",\r\n  FETCH_ALL_HOTEL: \"/hotel/get-all-hotel\",\r\n\r\n  //FEEDBACK:\r\n  FETCH_ROOM_DETAIL: \"/room/rooms_detail/:roomId\",\r\n  FETCH_TOP3_HOTEL:\"hotel/top-bookings\",\r\n  //FEEDBACK:\r\n  FETCH_FEEDBACK_BY_HOTELID: 'feedback/get-feedback-hotel/:hotelId',\r\n  LIKE_FEEDBACK: 'feedback/like',\r\n  DISLIKE_FEEDBACK: 'feedback/dislike',\r\n  FETCH_FEEDBACK_BY_ID:'feedback/getFeedbackById/:feedbackId',\r\n\r\n  //CREATE_BOOKING:\r\n  CREATE_BOOKING: 'payment/create-booking',\r\n  CHECKOUT_BOOKING: 'payment/checkout-booking',\r\n  CANCEL_PAYMENT: 'payment/cancel-payment',\r\n  ACCEPT_PAYMENT: 'payment/accept-payment',\r\n\r\n  //RESERVATION:\r\n  FETCH_DETAIL_RESERVATION: '/reservations/detail',\r\n  FETCH_USER_RESERVATIONS: \"reservations/get-reservation\",\r\n  FETCH_RESERVATION_DETAIL:\"reservations/reservations-detail/:id\",\r\n  UPDATE_RESERVATION:\"reservations/update-reservations/:id\",\r\n  \r\n  //FEEDBACK\r\n  FETCH_FEEDBACK_BY_HOTELID: \"feedback/get-feedback-hotel/:hotelId\",\r\n  FETCH_USER_FEEDBACKS: \"feedback/my-feedbacks\",\r\n  UPDATE_FEEDBACK: \"feedback/update-feedback/:feedbackId\",\r\n  DELETE_FEEDBACK: \"\",\r\n  CREATE_FEEDBACK:\"feedback/create-feedback\",\r\n  UPDATE_FEEDBACK_STATUS: \"feedback/updateStatusFeedback/:feedbackId\",\r\n\r\n  //REFUNDING_RESERVATION\r\n  CREATE_REFUNDING_RESERVATION: \"refunding_reservation/create\",\r\n  GET_REFUNDING_RESERVATION_BYUSERID: \"refunding_reservation/by_userId\",\r\n  UPDATE_BANKING_INFO: \"refunding_reservation/banking-info\",\r\n  \r\n  ///REPORTFEEDBACK\r\n  REPORT_FEEDBACK:\"reportFeedback/create_report_feedback\",\r\n  FETCH_REPORTS_BY_USERID:\"reportFeedback/my-reports\",\r\n  DELETE_REPORTED_FEEDBACK:\"reportFeedback/delete_report_feedback/:reportId\",\r\n  GET_ALL_REPORTED_FEEDBACKS: \"/reportFeedback/getReportedFeedbackDetails\",\r\n  GET_REPORTS_BY_FEEDBACK_ID: \"/reportFeedback/getReportedFeedbackByFeedbackId/:feedbackId\",\r\n  UPDATE_REPORT_STATUS: \"/reportFeedback/updateReportStatus/:reportId\",\r\n\r\n\r\n  //chat\r\n  FETCH_CHAT_MESSAGE: '/chat/chat-history/:receiverId',\r\n  FETCH_CHAT_ALL_USERS: '/chat/chat-users',\r\n\r\n  //ADMIN DASHBOARD\r\n  ADMIN_DASHBOARD_METRICS: '/dashboard-admin/metrics',\r\n\r\n  //MONTHLY PAYMENT (Admin)\r\n  FETCH_ALL_MONTHLY_PAYMENTS: \"/monthly-payment/admin/all\",\r\n  UPDATE_PAYMENT_STATUS: \"/monthly-payment/admin/:paymentId/status\",\r\n  GET_PAYMENT_BY_ID: \"/monthly-payment/admin/:paymentId\",\r\n\r\n  //PROMOTION (Admin)\r\n  FETCH_ALL_PROMOTIONS: \"/promotions\",\r\n  CREATE_PROMOTION: \"/promotions\",\r\n  UPDATE_PROMOTION: \"/promotions/:id\",\r\n  DELETE_PROMOTION: \"/promotions/:id\",\r\n  GET_PROMOTION_BY_ID: \"/promotions/:id\",\r\n  TOGGLE_PROMOTION_STATUS: \"/promotions/:id/status\",\r\n\r\n  // PROMOTION USER MANAGEMENT (Admin)\r\n  GET_PROMOTION_USERS: \"/promotions/:id/users\",\r\n  GET_PROMOTION_STATS: \"/promotions/:id/stats\",\r\n  GET_USER_PROMOTIONS: \"/promotions/users/:userId\",\r\n  REMOVE_USER_FROM_PROMOTION: \"/promotions/:id/users/:userId\",\r\n  RESET_USER_PROMOTION_USAGE: \"/promotions/:id/users/:userId/reset\",\r\n  GET_ALL_REFUND: \"/payment/getAllRefund\",\r\n  REFUND:\"payment/stripe-refund/:id\",\r\n  // CUSTOMER MANAGEMENT (Admin)\r\n  LOCK_CUSTOMER: \"/auth/lock-customer/:id\",\r\n  UNLOCK_CUSTOMER: \"/auth/unlock-customer/:id\",\r\n};\r\n\r\nexport default ApiConstants;\r\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG;EACnB;EACAC,cAAc,EAAE,sBAAsB;EACtCC,YAAY,EAAE,qBAAqB;EACnCC,iBAAiB,EAAE,yBAAyB;EAC5CC,cAAc,EAAE,8BAA8B;EAC9CC,eAAe,EAAE,+BAA+B;EAChDC,YAAY,EAAE,oBAAoB;EAClCC,mBAAmB,EAAE,2BAA2B;EAChDH,cAAc,EAAE,8BAA8B;EAC9CC,eAAe,EAAE,+BAA+B;EAChDG,aAAa,EAAE,qBAAqB;EACpCC,eAAe,EAAE,uBAAuB;EACxCC,cAAc,EAAE,sBAAsB;EACtCC,sBAAsB,EAAE,8BAA8B;EACtD;EACAC,YAAY,EAAE,SAAS;EACvBC,qBAAqB,EAAE,uBAAuB;EAC9CC,sBAAsB,EAAE,wBAAwB;EAChDC,mBAAmB,EAAE,qBAAqB;EAC1C;EACAC,mBAAmB,EAAE,8BAA8B;EACnDC,UAAU,EAAE,kCAAkC;EAC9CC,eAAe,EAAE,sBAAsB;EAEvC;EACAC,iBAAiB,EAAE,4BAA4B;EAC/CC,gBAAgB,EAAC,oBAAoB;EACrC;EACAC,yBAAyB,EAAE,sCAAsC;EACjEC,aAAa,EAAE,eAAe;EAC9BC,gBAAgB,EAAE,kBAAkB;EACpCC,oBAAoB,EAAC,sCAAsC;EAE3D;EACAC,cAAc,EAAE,wBAAwB;EACxCC,gBAAgB,EAAE,0BAA0B;EAC5CC,cAAc,EAAE,wBAAwB;EACxCC,cAAc,EAAE,wBAAwB;EAExC;EACAC,wBAAwB,EAAE,sBAAsB;EAChDC,uBAAuB,EAAE,8BAA8B;EACvDC,wBAAwB,EAAC,sCAAsC;EAC/DC,kBAAkB,EAAC,sCAAsC;EAEzD;EACAX,yBAAyB,EAAE,sCAAsC;EACjEY,oBAAoB,EAAE,uBAAuB;EAC7CC,eAAe,EAAE,sCAAsC;EACvDC,eAAe,EAAE,EAAE;EACnBC,eAAe,EAAC,0BAA0B;EAC1CC,sBAAsB,EAAE,2CAA2C;EAEnE;EACAC,4BAA4B,EAAE,8BAA8B;EAC5DC,kCAAkC,EAAE,iCAAiC;EACrEC,mBAAmB,EAAE,oCAAoC;EAEzD;EACAC,eAAe,EAAC,uCAAuC;EACvDC,uBAAuB,EAAC,2BAA2B;EACnDC,wBAAwB,EAAC,iDAAiD;EAC1EC,0BAA0B,EAAE,4CAA4C;EACxEC,0BAA0B,EAAE,6DAA6D;EACzFC,oBAAoB,EAAE,8CAA8C;EAGpE;EACAC,kBAAkB,EAAE,gCAAgC;EACpDC,oBAAoB,EAAE,kBAAkB;EAExC;EACAC,uBAAuB,EAAE,0BAA0B;EAEnD;EACAC,0BAA0B,EAAE,4BAA4B;EACxDC,qBAAqB,EAAE,0CAA0C;EACjEC,iBAAiB,EAAE,mCAAmC;EAEtD;EACAC,oBAAoB,EAAE,aAAa;EACnCC,gBAAgB,EAAE,aAAa;EAC/BC,gBAAgB,EAAE,iBAAiB;EACnCC,gBAAgB,EAAE,iBAAiB;EACnCC,mBAAmB,EAAE,iBAAiB;EACtCC,uBAAuB,EAAE,wBAAwB;EAEjD;EACAC,mBAAmB,EAAE,uBAAuB;EAC5CC,mBAAmB,EAAE,uBAAuB;EAC5CC,mBAAmB,EAAE,2BAA2B;EAChDC,0BAA0B,EAAE,+BAA+B;EAC3DC,0BAA0B,EAAE,qCAAqC;EACjEC,cAAc,EAAE,uBAAuB;EACvCC,MAAM,EAAC,2BAA2B;EAClC;EACAC,aAAa,EAAE,yBAAyB;EACxCC,eAAe,EAAE;AACnB,CAAC;AAED,eAAenE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}