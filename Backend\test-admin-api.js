// Test script to check admin API responses
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
const ADMIN_TOKEN = 'your-admin-token-here'; // Replace with actual admin token

const config = {
  headers: {
    'Authorization': `Bearer ${ADMIN_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

async function testAdminAPI() {
  console.log('🧪 TESTING ADMIN API RESPONSES\n');
  console.log('=' .repeat(50));

  try {
    // Test 1: Get all promotions with status=all
    console.log('1️⃣ Testing: GET /promotions?status=all&page=1&limit=50');
    const allResponse = await axios.get(`${BASE_URL}/promotions?status=all&page=1&limit=50`, config);
    
    console.log('📊 Response summary:');
    console.log(`   Total promotions: ${allResponse.data.promotions?.length || 0}`);
    console.log(`   Pagination total: ${allResponse.data.pagination?.totalPromotions || 0}`);
    console.log(`   Stats total: ${allResponse.data.stats?.total || 0}`);
    
    if (allResponse.data.promotions) {
      const types = allResponse.data.promotions.reduce((acc, p) => {
        acc[p.type] = (acc[p.type] || 0) + 1;
        return acc;
      }, {});
      console.log(`   Types: ${JSON.stringify(types)}`);
    }

    // Test 2: Get promotions with no status filter
    console.log('\n2️⃣ Testing: GET /promotions (no status filter)');
    const noStatusResponse = await axios.get(`${BASE_URL}/promotions?page=1&limit=50`, config);
    
    console.log('📊 Response summary:');
    console.log(`   Total promotions: ${noStatusResponse.data.promotions?.length || 0}`);
    console.log(`   Pagination total: ${noStatusResponse.data.pagination?.totalPromotions || 0}`);

    // Test 3: Get promotions with status=active
    console.log('\n3️⃣ Testing: GET /promotions?status=active');
    const activeResponse = await axios.get(`${BASE_URL}/promotions?status=active&page=1&limit=50`, config);
    
    console.log('📊 Response summary:');
    console.log(`   Active promotions: ${activeResponse.data.promotions?.length || 0}`);
    console.log(`   Pagination total: ${activeResponse.data.pagination?.totalPromotions || 0}`);

    // Test 4: Compare with customer API (no auth)
    console.log('\n4️⃣ Testing: Customer API (no auth)');
    try {
      const customerResponse = await axios.get(`${BASE_URL}/promotions?status=all&page=1&limit=50`);
      
      console.log('📊 Customer response summary:');
      console.log(`   Total promotions: ${customerResponse.data.promotions?.length || 0}`);
      console.log(`   Pagination total: ${customerResponse.data.pagination?.totalPromotions || 0}`);
      
      if (customerResponse.data.promotions) {
        const types = customerResponse.data.promotions.reduce((acc, p) => {
          acc[p.type] = (acc[p.type] || 0) + 1;
          return acc;
        }, {});
        console.log(`   Types: ${JSON.stringify(types)}`);
      }
    } catch (error) {
      console.log('   Customer API error:', error.response?.data?.message || error.message);
    }

    console.log('\n' + '=' .repeat(50));
    console.log('✅ API tests complete!');

  } catch (error) {
    console.error('❌ API test failed:', error.response?.data || error.message);
  }
}

// Instructions
console.log('⚠️  IMPORTANT: Update ADMIN_TOKEN before running this test!');
console.log('💡 To get admin token:');
console.log('   1. Login as admin in the app');
console.log('   2. Check localStorage or network requests for the token');
console.log('   3. Replace ADMIN_TOKEN in this file');
console.log('   4. Make sure backend server is running on port 5000\n');

// Uncomment to run tests
// testAdminAPI();
