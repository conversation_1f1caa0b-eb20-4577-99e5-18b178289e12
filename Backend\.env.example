PORT=5000
SECRET_KEY=hoangnguyen
MONGODB_URI_DEVELOPMENT=mongodb://127.0.0.1:27017/My_Uroom
MONGODB_URI_PRODUCTION=mongodb+srv://lkhnguyen3006:<EMAIL>/My_Uroom
ISSUER=issuer
ENVIRONMENT=production

#Cloudinary
CLOUD_NAME=dnel8ng9g
CLOUD_KEY=***************
CLOUD_KEY_SECRET=DH_FeyH7KvujMJEWgQDD5s-4C-c

#email
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=iidd jshc rwob zyne

#firebase
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=uroom-dc352
FIREBASE_PRIVATE_KEY_ID=ac671015a7d5b08db64137edc988d8d52bb052d9
FIREBASE_PRIVATE_KEY="-----B<PERSON>IN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCu6A5knbKJNtF/\ntQxvnIZi9jI39n1rJM/3AG38e3/Mqabaz6/p3Waz2SuR1ZWTEOM6V+mkxq//1XXV\nlRGRzzlSW0m7/zfqBp2LSQ3Wx1bRv1RLHLJr2W2YZnpwymaY3FrwIjsuVOVt4E2m\nEclOTAQqRXE91omEsoddI9qtnhz4eRJP8cnU2mXXH4RuM8JJoG5zi039eXUSPRBe\nMB5eFFyCYQpy/7PvQvfu/GTje4JyjtVN7Uac6v+DTLqf50ZhJABSP1fIl7Zu2pS6\nV4hyBa1uWZeuKh26kHH1kjysBY0hrHNLfZOncjdiyB4O3pD3L7U5Pwjk1u5mKEvy\ns7B51gopAgMBAAECggEADUg0fZoHGonl51mHj/s2LlItVpgahpYlT8fB0mxLdG6G\nq/OdE3ibablqaFjskp4z7zSJWti2uUoLuHQpQSYYH4OsqgDTzWsCsDbUPPTmyCdM\nHqcXen6opqvTjPilyWJkanti/J34TTt9EgkkPRSjNVWQJ6bAyymmg2FrZAdPgmPJ\nBdApdm2hUYRl+tE2m5PfvJbNKgZi/I+Yv3A+y2k1skiT0DrJVYjEafideUJK+PHv\nxIJGoGH1oBmF54pjupXNR45/35F05pMLsPt/TniBEogUOEgLMIH9Obr7FPIw2yFo\nqmoM3B3bW04eYEsPq4tVIVerG/nsSoqnsTHItUVuYQKBgQDnrjQQpcU3hq1hpo4M\nKLpkIzOuwOlZu6BtWIQpVshmNSp7P8WGPKznwTDFjRRMW1h0JQ0EiE+tLg3VedZO\nnxzJxLCbw6ThX0cva9U96hrY/9lL+Ui75SxwFenAHl3vRqDirpAF5sQBpjnKASBm\nKlzJy3lMifP7FJgPPUxAAvG4cQKBgQDBRDPJsPZA1m8Mttes9RYRfdNOsRU7F4R1\nFcxaEqBAqEWqJrw58wjGd5VbXfoLoNRtqYXv0jG+BJTWQPd6ixo2x2L2mucN5kkk\nd1RARTxy4BjJgh9AjLyI96gi2N7cEVNb2HHhOtmtahMSGa4RkFGD799a3sz44DhN\nfEt//wgJOQKBgBf/Dx16TgcGSAcJPUU9IrEkhEHhcUn+CQvKU0YE2xTbBN9nJ4pT\n1oTambacejG+dFbzT3nctIP3dS9bQzs5d73eRXycswM03yamQJv9F3LKbTNVSpdL\nkWK6ok9n1crmwds4wWobEMoq8Wle3IA4ewvbq2CmGUM0StupObCXnXnBAoGAZxZG\n/WUb5Pe8Ii8BGatdCxVvecDxDyT7LSzkZIMc4YAgMKMasVpWKu6sRyD4d+1ggeHz\npDkc+TseXK4okz/RSrjNq7ZyWjKkfwP7d5C2rpuj/RAgNWEcS362O2gsb3v6V7o6\n/CVU6K/03im+dHXjVRnErIa9FF8dCQ+sgeh509kCgYEAliNAMlFVx4tIKBqKUlY+\n68e7H6Vb3GiAaXhF9oo0HL2UndKGxFvI9+wbO0SI6ukavb5fl91dyo+LnprX7bGi\nn8bkJ9XjVDsHJQ4yc1Ds6IPPCHrUAW0eWeDz4g9OwhbDfWqUU7E0Vi9Ww95tKKPc\nnPnr1IBzPXLk7Pgt+Fhe2YQ=\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=109777155995155695565
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40uroom-dc352.iam.gserviceaccount.com
FIREBASE_UNIVERSE_DOMAIN=googleapis.com

#Gemini
GEMINI_API_KEY=AIzaSyDmAxvpaezupGzIoRCbkocgV9ovpBa29og

#BACKEND URL
FRONTEND_CUSTOMER_URL_PRODUCT=https://uroom.vercel.app
FRONTEND_CUSTOMER_URL_DEVELOPMENT=http://localhost:3000

FRONTEND_OWNER_URL_PRODUCT=https://uroomowner.vercel.app
FRONTEND_OWNER_URL_DEVELOPMENT=http://localhost:3001

FRONTEND_ADMIN_URL_DEVELOPMENT=http://localhost:3002
FRONTEND_ADMIN_URL_PRODUCT=https://uroom-admin.vercel.app

ENVIRONMENT=development

#Stripe
STRIPE_API_KEY=sk_test_51R2URb2NlbBRLqkFjVZfjkXaibZ7veQJu5PIzPkfGKEzYLx8cIG1tEomTUzY4Nc6wdz2NWlA29UOKzikJhEYrpW500zq2qoWCH
STRIPE_WEBHOOK_SECRET=whsec_sCYVgfRcb7DwG6KPTJCiUqrSwrRthfZF

#ngrok
#link run
ngrok http --url=gator-above-lately.ngrok-free.app 5000
#domain
gator-above-lately.ngrok-free.app