// src/middlewares/checkOwner.js

const jwt = require("jsonwebtoken");

const checkOwner = (req, res, next) => {
  const token = req.headers.authorization?.split(" ")[1];
  if (!token) return res.status(401).json({ message: "No token provided" });
  if (!token) {
    return res.status(401).json({ message: "No token provided" });
  }

  jwt.verify(token, process.env.SECRET_KEY, (err, decoded) => {
    if (err) {
      return res.status(403).json({ message: "Invalid token" });
    }

    // Ki<PERSON><PERSON> tra role
    if (!decoded?.user || decoded.user.role !== "OWNER") {
      return res.status(403).json({ message: "Access denied" });
    }

    req.user = decoded.user;
    next();
  });
};

module.exports = checkOwner;
