import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  ProgressBar,
  Table,
} from "react-bootstrap";
import {
  FaUsers,
  FaChartLine,
  FaPercentage,
  FaClock,
  FaCheckCircle,
  FaTimesCircle,
  FaExclamationTriangle,
} from "react-icons/fa";
import { getPromotionStats } from "../../redux/promotion/actions";

const PromotionStatsCard = ({ promotion }) => {
  const dispatch = useDispatch();
  const { promotionStats } = useSelector((state) => state.Promotion);

  useEffect(() => {
    if (promotion?._id) {
      loadPromotionStats();
    }
  }, [promotion]);

  const loadPromotionStats = () => {
    dispatch(
      getPromotionStats({
        promotionId: promotion._id,
        onSuccess: (data) => {
          console.log("✅ Promotion stats loaded:", data);
        },
        onFailed: (error) => {
          console.error("❌ Failed to load promotion stats:", error);
        },
      })
    );
  };

  if (promotionStats.loading) {
    return (
      <Card>
        <Card.Body className="text-center py-4">
          <Spinner animation="border" />
          <p className="mt-2">Loading statistics...</p>
        </Card.Body>
      </Card>
    );
  }

  if (promotionStats.error) {
    return (
      <Card>
        <Card.Body>
          <Alert variant="danger">{promotionStats.error}</Alert>
        </Card.Body>
      </Card>
    );
  }

  if (!promotionStats.data) {
    return null;
  }

  const { stats, statusBreakdown, usageDistribution, recentActivity } =
    promotionStats.data;

  const getUsageDistributionData = () => {
    return Object.entries(usageDistribution)
      .map(([count, users]) => ({
        count: parseInt(count),
        users: users,
      }))
      .sort((a, b) => a.count - b.count);
  };

  return (
    <Card>
      <Card.Header>
        <h5 className="mb-0">
          <FaChartLine className="me-2" />
          Promotion Statistics
        </h5>
      </Card.Header>
      <Card.Body>
        {/* Overview Stats */}
        <Row className="mb-4">
          <Col md={3}>
            <div className="stat-card text-center p-3 border rounded">
              <FaUsers className="text-primary mb-2" size={24} />
              <h4 className="mb-1">{stats.totalUsers}</h4>
              <small className="text-muted">Total Users</small>
            </div>
          </Col>
          <Col md={3}>
            <div className="stat-card text-center p-3 border rounded">
              <FaCheckCircle className="text-success mb-2" size={24} />
              <h4 className="mb-1">{stats.totalClaimed}</h4>
              <small className="text-muted">Claimed</small>
            </div>
          </Col>
          <Col md={3}>
            <div className="stat-card text-center p-3 border rounded">
              <FaChartLine className="text-info mb-2" size={24} />
              <h4 className="mb-1">{stats.totalUsed}</h4>
              <small className="text-muted">Used</small>
            </div>
          </Col>
          <Col md={3}>
            <div className="stat-card text-center p-3 border rounded">
              <FaPercentage className="text-warning mb-2" size={24} />
              <h4 className="mb-1">{stats.averageUsagePerUser}</h4>
              <small className="text-muted">Avg Usage</small>
            </div>
          </Col>
        </Row>

        {/* Rates */}
        <Row className="mb-4">
          <Col md={6}>
            <div className="mb-3">
              <div className="d-flex justify-content-between mb-1">
                <span>Claim Rate</span>
                <span>{stats.claimRate}%</span>
              </div>
              <ProgressBar
                now={parseFloat(stats.claimRate)}
                variant="success"
                style={{ height: "8px" }}
              />
            </div>
          </Col>
          <Col md={6}>
            <div className="mb-3">
              <div className="d-flex justify-content-between mb-1">
                <span>Usage Rate</span>
                <span>{stats.usageRate}%</span>
              </div>
              <ProgressBar
                now={parseFloat(stats.usageRate)}
                variant="info"
                style={{ height: "8px" }}
              />
            </div>
          </Col>
        </Row>

        {/* Status Breakdown */}
        <Row className="mb-4">
          <Col md={6}>
            <h6>Status Breakdown</h6>
            <div className="status-breakdown">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>
                  <Badge bg="secondary" className="me-2">
                    {statusBreakdown.not_claimed}
                  </Badge>
                  Not Claimed
                </span>
              </div>
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>
                  <Badge bg="info" className="me-2">
                    {statusBreakdown.claimed_not_used}
                  </Badge>
                  Claimed (Not Used)
                </span>
              </div>
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>
                  <Badge bg="success" className="me-2">
                    {statusBreakdown.active}
                  </Badge>
                  Active
                </span>
              </div>
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>
                  <Badge bg="warning" className="me-2">
                    {statusBreakdown.used_up}
                  </Badge>
                  Used Up
                </span>
              </div>
            </div>
          </Col>
          <Col md={6}>
            <h6>Recent Activity (30 days)</h6>
            <div className="recent-activity">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>
                  <FaClock className="text-primary me-2" />
                  Recent Claims
                </span>
                <Badge bg="primary">{recentActivity.recentClaims}</Badge>
              </div>
              <div className="d-flex justify-content-between align-items-center mb-2">
                <span>
                  <FaChartLine className="text-success me-2" />
                  Recent Usage
                </span>
                <Badge bg="success">{recentActivity.recentUsage}</Badge>
              </div>
            </div>
          </Col>
        </Row>

        {/* Usage Distribution */}
        <Row>
          <Col>
            <h6>Usage Distribution</h6>
            <Table size="sm" responsive>
              <thead>
                <tr>
                  <th>Usage Count</th>
                  <th>Number of Users</th>
                  <th>Percentage</th>
                </tr>
              </thead>
              <tbody>
                {getUsageDistributionData().map(({ count, users }) => (
                  <tr key={count}>
                    <td>
                      <Badge bg={count === 0 ? "secondary" : "primary"}>
                        {count}
                      </Badge>
                    </td>
                    <td>{users}</td>
                    <td>
                      {stats.totalUsers > 0
                        ? ((users / stats.totalUsers) * 100).toFixed(1)
                        : 0}
                      %
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </Col>
        </Row>

        {/* Promotion Limits Info */}
        {promotion && (
          <Row className="mt-4">
            <Col>
              <div className="promotion-limits p-3 bg-light rounded">
                <h6>Promotion Limits</h6>
                <Row>
                  <Col md={4}>
                    <strong>Usage Limit:</strong>{" "}
                    {promotion.usageLimit || "Unlimited"}
                  </Col>
                  <Col md={4}>
                    <strong>Used Count:</strong> {promotion.usedCount || 0}
                  </Col>
                  <Col md={4}>
                    <strong>Max Per User:</strong>{" "}
                    {promotion.maxUsagePerUser || 1}
                  </Col>
                </Row>
                {promotion.usageLimit && (
                  <div className="mt-2">
                    <div className="d-flex justify-content-between mb-1">
                      <span>Overall Usage</span>
                      <span>
                        {promotion.usedCount} / {promotion.usageLimit}
                      </span>
                    </div>
                    <ProgressBar
                      now={(promotion.usedCount / promotion.usageLimit) * 100}
                      variant={
                        promotion.usedCount / promotion.usageLimit > 0.8
                          ? "danger"
                          : promotion.usedCount / promotion.usageLimit > 0.6
                          ? "warning"
                          : "success"
                      }
                      style={{ height: "8px" }}
                    />
                  </div>
                )}
              </div>
            </Col>
          </Row>
        )}
      </Card.Body>
    </Card>
  );
};

export default PromotionStatsCard;
