body {
  margin: 0;
  padding: 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #F2F6FF;
}

.app-container {
  min-height: 100vh;
}

/* Navbar Styles */
/* .navbar {
  background-color: rgba(26, 43, 73, 0);
  padding: 1rem 0;
} */


/* Hero Section */
.hero-section {
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5)),
    url("../../images/banner.png");
  background-size: cover;
  background-position: center;
  height: 500px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-bottom: 3rem;
  position: relative; /* Để button không bị lệch */
}

.hero-content {
  color: white;
 
}

.hero-content h1 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.rating {
  display: flex;
  gap: 0.25rem;
 justify-content: center;

}

.star-filled {
  color: #ffc107;
  font-size: 1.25rem;
}

/* Main Content */
.main-content {
  margin-top: -2rem;
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
}

.content-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  margin-top: 5%;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.main-image-container {
  margin-bottom: 1.5rem;
}

.main-image {
  width: 90%;
  height: 500px;
  object-fit: cover;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.thumbnail-container {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.thumbnail {
  width: calc(30.333% - 0.67rem);
  height: 150px;
  object-fit: cover;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: opacity 0.2s;
}

.thumbnail:hover {
  opacity: 0.8;
}

.hotel-info {
  padding: 0 1rem;
}

.hotel-info h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1a2b49;
}

.hotel-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem;
  color: #1a2b49;
}

.hotel-info p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.highlights-list {
  list-style: none;
  padding-left: 0;
  margin-bottom: 1.5rem;
}

.highlights-list li {
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
  position: relative;
  color: #666;
}

.highlights-list li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #1a2b49;
}

.amenities-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-top: 1rem;
}

.amenity-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
}

.amenity-item svg {
  color: #1a2b49;
  font-size: 1.25rem;
}

.search-section {
  margin-top: 2rem;
  padding-top: 2rem;

}

/* Hotel Rooms Section */
.rooms-section {
  padding: 4rem 0;
  background-color: #F2F6FF;
}

.section-title {
  text-align: center;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 3rem;
  color: #1a2b49;
}

.room-card {
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  border: none;
}

.room-image-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.room-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
 
}

.room-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.guests-count {
  background-color: #f8f9fa;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.9rem;
  color: #666;
}

.price-container {
  display: flex;
  align-items: center;
  margin-top: 1rem;
}
/* .price-container_1 {
  display: flex;
  margin-top: 1rem;
} */

.price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1a2b49;
}

.per-day {
  color: #666;
  margin-left: 0.25rem;
}

.amount-container {
  display: flex;
  align-items: center;
  background-color: #f5f8ff; /* Màu nền xanh nhạt */
  padding: 10px 15px;
  border-radius: 10px;
  width: fit-content;
  margin-left: auto;
}

.label {
  font-weight: bold;
  color: #2374e1; /* Màu xanh */
  margin-right: 8px;
  font-size: 16px;
}

.amount-dropdown {
  border: none;
  background: white;
  border-radius: 6px;
  padding: 5px 10px;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  outline: none;
}

/* Tùy chỉnh mũi tên dropdown */
.amount-dropdown::-ms-expand {
  display: none;
}

.amount-dropdown:focus {
  outline: none;
}


/* Other Hotels Section */
/* .other-hotels-section {
  padding-top: 5%;
  padding: 4rem 0;
} */

.hotel-card {
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  border: none;
}

.hotel-image-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.hotel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}



.rating-overlay {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.25rem;
}

.star-icon {
  color: #ffc107;
  font-size: 1rem;
}

.hotel-name {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.room-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.room-type {
  color: #666;
  font-size: 0.9rem;
}

/* .book-now-btn {
  padding: 0.7rem 4.5rem;
  font-weight: 500;
  background-color: white;
  color: #0d6efd;
} */
.reviews-section {
  text-align: center;
  padding: 40px 0;
  background-color: #f5f7fc; /* Màu nền nhạt */
}

.reviews-title {
  text-align: center;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 3rem;
  color: #1a2b49;
}
.review-card {
  background: white;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
}

.quote-icon {
  font-size: 40px;
  color: #ccc;
}

.review-text {
  font-size: 16px;
  font-weight: 500;
  margin: 20px 0;
}

.review-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin: 10px 0;
}

.action-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #666;
  transition: color 0.3s ease-in-out;
}

.action-btn:hover {
  color: #000;
}

.reviewer-info {
  margin-top: 20px;
}

.reviewer-name {
  font-size: 16px;
  font-weight: bold;
  color: #007bff;
}

.reviewer-status {
  font-size: 14px;
  color: #888;
  margin-bottom: 10px;
}

.reviewer-profile {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.reviewer-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.rating {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: bold;
}

.star-icon {
  color: gold;
  margin-right: 5px;
}


@media (max-width: 768px) {
  .hero-section {
    height: 300px;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .main-content {
    margin-top: -1rem;
  }

  .content-card {
    padding: 1rem;
  }

  .hotel-info {
    padding: 1rem 0;
  }

  .amenities-grid {
    grid-template-columns: 1fr;
    /* justify-content: center; */
  }

  .section-title {
    font-size: 1.75rem;
    margin-bottom: 2rem;
  }

  .room-image-container,
  .hotel-image-container {
    height: 180px;
  }
}


/* .social-links svg {
  color: #f8e71c;
} */


