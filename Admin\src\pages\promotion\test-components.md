# Frontend Component Testing Guide

## Manual Testing Checklist

### 1. ListPromotionPage Testing

#### New Action Buttons
- [ ] "Users" button (👥) appears for each promotion
- [ ] "Statistics" button (📊) appears for each promotion
- [ ] Buttons are properly styled and responsive
- [ ] Hover effects work correctly
- [ ] Buttons are disabled during loading states

#### Users Modal
- [ ] <PERSON><PERSON> opens when "Users" button is clicked
- [ ] Modal displays correct promotion code in title
- [ ] Search functionality works
- [ ] Status filter works (All, Claimed, Used)
- [ ] Sorting works for all columns
- [ ] Pagination works correctly
- [ ] "Reset Usage" button works
- [ ] "Remove User" button works
- [ ] Confirmation modals appear for actions
- [ ] <PERSON>dal closes properly

#### Statistics Modal
- [ ] Modal opens when "Statistics" button is clicked
- [ ] Statistics card loads and displays data
- [ ] All statistics sections are visible
- [ ] Progress bars animate correctly
- [ ] Modal closes properly

### 2. DetailPromotionPage Testing

#### Tab Navigation
- [ ] "Details" tab is active by default
- [ ] "Statistics" tab appears in view mode
- [ ] Tab switching works smoothly
- [ ] Content updates correctly when switching tabs

#### Statistics Tab
- [ ] PromotionStatsCard loads correctly
- [ ] All statistics sections display properly
- [ ] Data is formatted correctly
- [ ] Loading states work

### 3. PromotionUsersModal Testing

#### Data Display
- [ ] User information displays correctly
- [ ] Usage counts are accurate
- [ ] Status badges show correct colors
- [ ] Dates are formatted properly

#### Functionality
- [ ] Search filters users correctly
- [ ] Status filter works
- [ ] Sorting changes order correctly
- [ ] Pagination navigates properly

#### Actions
- [ ] Reset usage shows confirmation
- [ ] Remove user shows confirmation
- [ ] Actions update data correctly
- [ ] Loading states during actions

### 4. PromotionStatsCard Testing

#### Overview Statistics
- [ ] Total users count is correct
- [ ] Claimed count is accurate
- [ ] Used count is accurate
- [ ] Average usage is calculated correctly

#### Progress Bars
- [ ] Claim rate displays correctly
- [ ] Usage rate displays correctly
- [ ] Progress bars animate smoothly
- [ ] Percentages are accurate

#### Status Breakdown
- [ ] All status categories display
- [ ] Counts are accurate
- [ ] Badges show correct colors

#### Recent Activity
- [ ] Recent claims count is correct
- [ ] Recent usage count is correct
- [ ] 30-day period is accurate

#### Usage Distribution
- [ ] Table displays all usage counts
- [ ] User counts are accurate
- [ ] Percentages are calculated correctly

#### Promotion Limits
- [ ] Usage limit displays correctly
- [ ] Used count is accurate
- [ ] Max per user is correct
- [ ] Progress bar shows correct percentage

### 5. Redux State Testing

#### Actions Dispatch
- [ ] getPromotionUsers dispatches correctly
- [ ] getPromotionStats dispatches correctly
- [ ] removeUserFromPromotion dispatches correctly
- [ ] resetUserPromotionUsage dispatches correctly

#### State Updates
- [ ] Loading states update correctly
- [ ] Success states update data
- [ ] Error states display messages
- [ ] Pagination state updates

### 6. API Integration Testing

#### Network Requests
- [ ] GET /promotions/:id/users works
- [ ] GET /promotions/:id/stats works
- [ ] DELETE /promotions/:id/users/:userId works
- [ ] PUT /promotions/:id/users/:userId/reset works

#### Error Handling
- [ ] Network errors display properly
- [ ] 404 errors are handled
- [ ] 403 errors show permission messages
- [ ] 500 errors show server error messages

### 7. Responsive Design Testing

#### Mobile (< 576px)
- [ ] Action buttons stack properly
- [ ] Modals are responsive
- [ ] Tables scroll horizontally
- [ ] Statistics cards stack vertically

#### Tablet (576px - 768px)
- [ ] Layout adjusts correctly
- [ ] Action buttons remain usable
- [ ] Modals fit screen properly

#### Desktop (> 768px)
- [ ] Full layout displays correctly
- [ ] All features are accessible
- [ ] Hover effects work

### 8. Performance Testing

#### Loading Times
- [ ] Components load quickly
- [ ] Large user lists paginate efficiently
- [ ] Statistics calculate quickly

#### Memory Usage
- [ ] No memory leaks when opening/closing modals
- [ ] State cleanup works properly
- [ ] Component unmounting is clean

## Common Issues to Check

### 1. Data Loading
- Ensure promotion data is available before rendering
- Check for null/undefined values
- Verify API responses match expected format

### 2. State Management
- Confirm Redux actions are dispatched
- Verify state updates correctly
- Check for race conditions

### 3. UI/UX
- Ensure loading states are visible
- Confirm error messages are helpful
- Verify success feedback is clear

### 4. Accessibility
- Check keyboard navigation
- Verify screen reader compatibility
- Ensure proper ARIA labels

## Test Data Requirements

### Minimum Test Data
- At least 1 promotion with users
- At least 1 user with promotion usage
- Mix of claimed/used/unused promotions
- Various usage counts for distribution testing

### Recommended Test Data
- 5+ promotions with different statuses
- 20+ users with various usage patterns
- Mix of percentage and fixed amount promotions
- Promotions with different limits and dates

## Browser Testing

### Supported Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Features to Test in Each Browser
- Modal functionality
- CSS animations
- API requests
- Local storage
- Responsive design

## Debugging Tips

### Common Issues
1. **Modal not opening**: Check Redux state and action dispatch
2. **Data not loading**: Verify API endpoints and authentication
3. **Styling issues**: Check CSS class names and imports
4. **State not updating**: Verify reducer logic and action types

### Debug Tools
- Redux DevTools for state inspection
- Network tab for API requests
- Console for error messages
- React DevTools for component inspection

## Performance Optimization

### Best Practices Implemented
- Pagination for large datasets
- Lazy loading of statistics
- Memoized components where appropriate
- Efficient Redux state structure
- Optimized API queries

### Monitoring
- Watch for slow API responses
- Monitor Redux state size
- Check for unnecessary re-renders
- Verify memory usage patterns
