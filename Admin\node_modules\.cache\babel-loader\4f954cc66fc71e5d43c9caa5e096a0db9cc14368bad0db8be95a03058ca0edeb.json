{"ast": null, "code": "import ApiConstants from \"../../adapter/ApiConstants\";\nimport api from \"../../libs/api/index\";\nconst Factories = {\n  // Fetch all promotions with pagination and filters\n  fetchAllPromotions: (params = {}) => {\n    const queryParams = new URLSearchParams();\n\n    // Add pagination params\n    if (params.page) queryParams.append('page', params.page);\n    if (params.limit) queryParams.append('limit', params.limit);\n\n    // Add filter params\n    if (params.search) queryParams.append('search', params.search);\n    if (params.status && params.status !== 'all') queryParams.append('status', params.status);\n    if (params.sortBy) queryParams.append('sortBy', params.sortBy);\n    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);\n    const url = `${ApiConstants.FETCH_ALL_PROMOTIONS}?${queryParams.toString()}`;\n    return api.get(url);\n  },\n  // Create new promotion\n  createPromotion: data => {\n    return api.post(ApiConstants.CREATE_PROMOTION, data);\n  },\n  // Update promotion\n  updatePromotion: (id, data) => {\n    const url = ApiConstants.UPDATE_PROMOTION.replace(\":id\", id);\n    return api.put(url, data);\n  },\n  // Delete promotion\n  deletePromotion: id => {\n    const url = ApiConstants.DELETE_PROMOTION.replace(\":id\", id);\n    return api.delete(url);\n  },\n  // Get promotion by ID\n  getPromotionById: id => {\n    const url = ApiConstants.GET_PROMOTION_BY_ID.replace(\":id\", id);\n    return api.get(url);\n  },\n  // Toggle promotion status\n  togglePromotionStatus: (id, status) => {\n    const url = ApiConstants.TOGGLE_PROMOTION_STATUS.replace(\":id\", id);\n    return api.patch(url, {\n      isActive: status\n    });\n  },\n  // ===== PROMOTION USER MANAGEMENT =====\n\n  // Get users who have claimed/used a specific promotion\n  getPromotionUsers: (id, params = {}) => {\n    const queryParams = new URLSearchParams();\n\n    // Add pagination params\n    if (params.page) queryParams.append('page', params.page);\n    if (params.limit) queryParams.append('limit', params.limit);\n\n    // Add filter params\n    if (params.search) queryParams.append('search', params.search);\n    if (params.status && params.status !== 'all') queryParams.append('status', params.status);\n    if (params.sortBy) queryParams.append('sortBy', params.sortBy);\n    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);\n    const url = ApiConstants.GET_PROMOTION_USERS.replace(\":id\", id);\n    return api.get(`${url}?${queryParams.toString()}`);\n  },\n  // Get all promotions for a specific user\n  getUserPromotions: (userId, params = {}) => {\n    const queryParams = new URLSearchParams();\n\n    // Add pagination params\n    if (params.page) queryParams.append('page', params.page);\n    if (params.limit) queryParams.append('limit', params.limit);\n    const url = ApiConstants.GET_USER_PROMOTIONS.replace(\":userId\", userId);\n    return api.get(`${url}?${queryParams.toString()}`);\n  },\n  // Remove user from promotion\n  removeUserFromPromotion: (promotionId, userId) => {\n    const url = ApiConstants.REMOVE_USER_FROM_PROMOTION.replace(\":id\", promotionId).replace(\":userId\", userId);\n    return api.delete(url);\n  },\n  // Reset user's usage count for a promotion\n  resetUserPromotionUsage: (promotionId, userId) => {\n    const url = ApiConstants.RESET_USER_PROMOTION_USAGE.replace(\":id\", promotionId).replace(\":userId\", userId);\n    return api.put(url);\n  },\n  // ===== ASSIGN PROMOTION =====\n\n  // Search users for assignment\n  searchUsersForAssignment: (promotionId, params = {}) => {\n    const queryParams = new URLSearchParams();\n\n    // Add pagination params\n    if (params.page) queryParams.append('page', params.page);\n    if (params.limit) queryParams.append('limit', params.limit);\n\n    // Add search params\n    if (params.search) queryParams.append('search', params.search);\n    const url = ApiConstants.SEARCH_USERS_FOR_ASSIGNMENT.replace(\":id\", promotionId);\n    return api.get(`${url}?${queryParams.toString()}`);\n  },\n  // Assign promotion to users\n  assignPromotionToUsers: (promotionId, userIds) => {\n    const url = ApiConstants.ASSIGN_PROMOTION_TO_USERS.replace(\":id\", promotionId);\n    return api.post(url, {\n      userIds\n    });\n  }\n};\nexport default Factories;", "map": {"version": 3, "names": ["ApiConstants", "api", "Factories", "fetchAllPromotions", "params", "queryParams", "URLSearchParams", "page", "append", "limit", "search", "status", "sortBy", "sortOrder", "url", "FETCH_ALL_PROMOTIONS", "toString", "get", "createPromotion", "data", "post", "CREATE_PROMOTION", "updatePromotion", "id", "UPDATE_PROMOTION", "replace", "put", "deletePromotion", "DELETE_PROMOTION", "delete", "getPromotionById", "GET_PROMOTION_BY_ID", "togglePromotionStatus", "TOGGLE_PROMOTION_STATUS", "patch", "isActive", "getPromotionUsers", "GET_PROMOTION_USERS", "getUserPromotions", "userId", "GET_USER_PROMOTIONS", "removeUserFromPromotion", "promotionId", "REMOVE_USER_FROM_PROMOTION", "resetUserPromotionUsage", "RESET_USER_PROMOTION_USAGE", "searchUsersForAssignment", "SEARCH_USERS_FOR_ASSIGNMENT", "assignPromotionToUsers", "userIds", "ASSIGN_PROMOTION_TO_USERS"], "sources": ["E:/Uroom/Admin/src/redux/promotion/factories.js"], "sourcesContent": ["import ApiConstants from \"../../adapter/ApiConstants\";\r\nimport api from \"../../libs/api/index\";\r\n\r\nconst Factories = {\r\n  // Fetch all promotions with pagination and filters\r\n  fetchAllPromotions: (params = {}) => {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add pagination params\r\n    if (params.page) queryParams.append('page', params.page);\r\n    if (params.limit) queryParams.append('limit', params.limit);\r\n\r\n    // Add filter params\r\n    if (params.search) queryParams.append('search', params.search);\r\n    if (params.status && params.status !== 'all') queryParams.append('status', params.status);\r\n    if (params.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n\r\n    const url = `${ApiConstants.FETCH_ALL_PROMOTIONS}?${queryParams.toString()}`;\r\n    return api.get(url);\r\n  },\r\n\r\n  // Create new promotion\r\n  createPromotion: (data) => {\r\n    return api.post(ApiConstants.CREATE_PROMOTION, data);\r\n  },\r\n\r\n  // Update promotion\r\n  updatePromotion: (id, data) => {\r\n    const url = ApiConstants.UPDATE_PROMOTION.replace(\":id\", id);\r\n    return api.put(url, data);\r\n  },\r\n\r\n  // Delete promotion\r\n  deletePromotion: (id) => {\r\n    const url = ApiConstants.DELETE_PROMOTION.replace(\":id\", id);\r\n    return api.delete(url);\r\n  },\r\n\r\n  // Get promotion by ID\r\n  getPromotionById: (id) => {\r\n    const url = ApiConstants.GET_PROMOTION_BY_ID.replace(\":id\", id);\r\n    return api.get(url);\r\n  },\r\n\r\n  // Toggle promotion status\r\n  togglePromotionStatus: (id, status) => {\r\n    const url = ApiConstants.TOGGLE_PROMOTION_STATUS.replace(\":id\", id);\r\n    return api.patch(url, { isActive: status });\r\n  },\r\n\r\n  // ===== PROMOTION USER MANAGEMENT =====\r\n\r\n  // Get users who have claimed/used a specific promotion\r\n  getPromotionUsers: (id, params = {}) => {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add pagination params\r\n    if (params.page) queryParams.append('page', params.page);\r\n    if (params.limit) queryParams.append('limit', params.limit);\r\n\r\n    // Add filter params\r\n    if (params.search) queryParams.append('search', params.search);\r\n    if (params.status && params.status !== 'all') queryParams.append('status', params.status);\r\n    if (params.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n\r\n    const url = ApiConstants.GET_PROMOTION_USERS.replace(\":id\", id);\r\n    return api.get(`${url}?${queryParams.toString()}`);\r\n  },\r\n\r\n\r\n\r\n  // Get all promotions for a specific user\r\n  getUserPromotions: (userId, params = {}) => {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add pagination params\r\n    if (params.page) queryParams.append('page', params.page);\r\n    if (params.limit) queryParams.append('limit', params.limit);\r\n\r\n    const url = ApiConstants.GET_USER_PROMOTIONS.replace(\":userId\", userId);\r\n    return api.get(`${url}?${queryParams.toString()}`);\r\n  },\r\n\r\n  // Remove user from promotion\r\n  removeUserFromPromotion: (promotionId, userId) => {\r\n    const url = ApiConstants.REMOVE_USER_FROM_PROMOTION\r\n      .replace(\":id\", promotionId)\r\n      .replace(\":userId\", userId);\r\n    return api.delete(url);\r\n  },\r\n\r\n  // Reset user's usage count for a promotion\r\n  resetUserPromotionUsage: (promotionId, userId) => {\r\n    const url = ApiConstants.RESET_USER_PROMOTION_USAGE\r\n      .replace(\":id\", promotionId)\r\n      .replace(\":userId\", userId);\r\n    return api.put(url);\r\n  },\r\n\r\n  // ===== ASSIGN PROMOTION =====\r\n\r\n  // Search users for assignment\r\n  searchUsersForAssignment: (promotionId, params = {}) => {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    // Add pagination params\r\n    if (params.page) queryParams.append('page', params.page);\r\n    if (params.limit) queryParams.append('limit', params.limit);\r\n\r\n    // Add search params\r\n    if (params.search) queryParams.append('search', params.search);\r\n\r\n    const url = ApiConstants.SEARCH_USERS_FOR_ASSIGNMENT.replace(\":id\", promotionId);\r\n    return api.get(`${url}?${queryParams.toString()}`);\r\n  },\r\n\r\n  // Assign promotion to users\r\n  assignPromotionToUsers: (promotionId, userIds) => {\r\n    const url = ApiConstants.ASSIGN_PROMOTION_TO_USERS.replace(\":id\", promotionId);\r\n    return api.post(url, { userIds });\r\n  },\r\n};\r\n\r\nexport default Factories;\r\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,4BAA4B;AACrD,OAAOC,GAAG,MAAM,sBAAsB;AAEtC,MAAMC,SAAS,GAAG;EAChB;EACAC,kBAAkB,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAK;IACnC,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;;IAEzC;IACA,IAAIF,MAAM,CAACG,IAAI,EAAEF,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEJ,MAAM,CAACG,IAAI,CAAC;IACxD,IAAIH,MAAM,CAACK,KAAK,EAAEJ,WAAW,CAACG,MAAM,CAAC,OAAO,EAAEJ,MAAM,CAACK,KAAK,CAAC;;IAE3D;IACA,IAAIL,MAAM,CAACM,MAAM,EAAEL,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACM,MAAM,CAAC;IAC9D,IAAIN,MAAM,CAACO,MAAM,IAAIP,MAAM,CAACO,MAAM,KAAK,KAAK,EAAEN,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACO,MAAM,CAAC;IACzF,IAAIP,MAAM,CAACQ,MAAM,EAAEP,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACQ,MAAM,CAAC;IAC9D,IAAIR,MAAM,CAACS,SAAS,EAAER,WAAW,CAACG,MAAM,CAAC,WAAW,EAAEJ,MAAM,CAACS,SAAS,CAAC;IAEvE,MAAMC,GAAG,GAAG,GAAGd,YAAY,CAACe,oBAAoB,IAAIV,WAAW,CAACW,QAAQ,CAAC,CAAC,EAAE;IAC5E,OAAOf,GAAG,CAACgB,GAAG,CAACH,GAAG,CAAC;EACrB,CAAC;EAED;EACAI,eAAe,EAAGC,IAAI,IAAK;IACzB,OAAOlB,GAAG,CAACmB,IAAI,CAACpB,YAAY,CAACqB,gBAAgB,EAAEF,IAAI,CAAC;EACtD,CAAC;EAED;EACAG,eAAe,EAAEA,CAACC,EAAE,EAAEJ,IAAI,KAAK;IAC7B,MAAML,GAAG,GAAGd,YAAY,CAACwB,gBAAgB,CAACC,OAAO,CAAC,KAAK,EAAEF,EAAE,CAAC;IAC5D,OAAOtB,GAAG,CAACyB,GAAG,CAACZ,GAAG,EAAEK,IAAI,CAAC;EAC3B,CAAC;EAED;EACAQ,eAAe,EAAGJ,EAAE,IAAK;IACvB,MAAMT,GAAG,GAAGd,YAAY,CAAC4B,gBAAgB,CAACH,OAAO,CAAC,KAAK,EAAEF,EAAE,CAAC;IAC5D,OAAOtB,GAAG,CAAC4B,MAAM,CAACf,GAAG,CAAC;EACxB,CAAC;EAED;EACAgB,gBAAgB,EAAGP,EAAE,IAAK;IACxB,MAAMT,GAAG,GAAGd,YAAY,CAAC+B,mBAAmB,CAACN,OAAO,CAAC,KAAK,EAAEF,EAAE,CAAC;IAC/D,OAAOtB,GAAG,CAACgB,GAAG,CAACH,GAAG,CAAC;EACrB,CAAC;EAED;EACAkB,qBAAqB,EAAEA,CAACT,EAAE,EAAEZ,MAAM,KAAK;IACrC,MAAMG,GAAG,GAAGd,YAAY,CAACiC,uBAAuB,CAACR,OAAO,CAAC,KAAK,EAAEF,EAAE,CAAC;IACnE,OAAOtB,GAAG,CAACiC,KAAK,CAACpB,GAAG,EAAE;MAAEqB,QAAQ,EAAExB;IAAO,CAAC,CAAC;EAC7C,CAAC;EAED;;EAEA;EACAyB,iBAAiB,EAAEA,CAACb,EAAE,EAAEnB,MAAM,GAAG,CAAC,CAAC,KAAK;IACtC,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;;IAEzC;IACA,IAAIF,MAAM,CAACG,IAAI,EAAEF,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEJ,MAAM,CAACG,IAAI,CAAC;IACxD,IAAIH,MAAM,CAACK,KAAK,EAAEJ,WAAW,CAACG,MAAM,CAAC,OAAO,EAAEJ,MAAM,CAACK,KAAK,CAAC;;IAE3D;IACA,IAAIL,MAAM,CAACM,MAAM,EAAEL,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACM,MAAM,CAAC;IAC9D,IAAIN,MAAM,CAACO,MAAM,IAAIP,MAAM,CAACO,MAAM,KAAK,KAAK,EAAEN,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACO,MAAM,CAAC;IACzF,IAAIP,MAAM,CAACQ,MAAM,EAAEP,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACQ,MAAM,CAAC;IAC9D,IAAIR,MAAM,CAACS,SAAS,EAAER,WAAW,CAACG,MAAM,CAAC,WAAW,EAAEJ,MAAM,CAACS,SAAS,CAAC;IAEvE,MAAMC,GAAG,GAAGd,YAAY,CAACqC,mBAAmB,CAACZ,OAAO,CAAC,KAAK,EAAEF,EAAE,CAAC;IAC/D,OAAOtB,GAAG,CAACgB,GAAG,CAAC,GAAGH,GAAG,IAAIT,WAAW,CAACW,QAAQ,CAAC,CAAC,EAAE,CAAC;EACpD,CAAC;EAID;EACAsB,iBAAiB,EAAEA,CAACC,MAAM,EAAEnC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC1C,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;;IAEzC;IACA,IAAIF,MAAM,CAACG,IAAI,EAAEF,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEJ,MAAM,CAACG,IAAI,CAAC;IACxD,IAAIH,MAAM,CAACK,KAAK,EAAEJ,WAAW,CAACG,MAAM,CAAC,OAAO,EAAEJ,MAAM,CAACK,KAAK,CAAC;IAE3D,MAAMK,GAAG,GAAGd,YAAY,CAACwC,mBAAmB,CAACf,OAAO,CAAC,SAAS,EAAEc,MAAM,CAAC;IACvE,OAAOtC,GAAG,CAACgB,GAAG,CAAC,GAAGH,GAAG,IAAIT,WAAW,CAACW,QAAQ,CAAC,CAAC,EAAE,CAAC;EACpD,CAAC;EAED;EACAyB,uBAAuB,EAAEA,CAACC,WAAW,EAAEH,MAAM,KAAK;IAChD,MAAMzB,GAAG,GAAGd,YAAY,CAAC2C,0BAA0B,CAChDlB,OAAO,CAAC,KAAK,EAAEiB,WAAW,CAAC,CAC3BjB,OAAO,CAAC,SAAS,EAAEc,MAAM,CAAC;IAC7B,OAAOtC,GAAG,CAAC4B,MAAM,CAACf,GAAG,CAAC;EACxB,CAAC;EAED;EACA8B,uBAAuB,EAAEA,CAACF,WAAW,EAAEH,MAAM,KAAK;IAChD,MAAMzB,GAAG,GAAGd,YAAY,CAAC6C,0BAA0B,CAChDpB,OAAO,CAAC,KAAK,EAAEiB,WAAW,CAAC,CAC3BjB,OAAO,CAAC,SAAS,EAAEc,MAAM,CAAC;IAC7B,OAAOtC,GAAG,CAACyB,GAAG,CAACZ,GAAG,CAAC;EACrB,CAAC;EAED;;EAEA;EACAgC,wBAAwB,EAAEA,CAACJ,WAAW,EAAEtC,MAAM,GAAG,CAAC,CAAC,KAAK;IACtD,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;;IAEzC;IACA,IAAIF,MAAM,CAACG,IAAI,EAAEF,WAAW,CAACG,MAAM,CAAC,MAAM,EAAEJ,MAAM,CAACG,IAAI,CAAC;IACxD,IAAIH,MAAM,CAACK,KAAK,EAAEJ,WAAW,CAACG,MAAM,CAAC,OAAO,EAAEJ,MAAM,CAACK,KAAK,CAAC;;IAE3D;IACA,IAAIL,MAAM,CAACM,MAAM,EAAEL,WAAW,CAACG,MAAM,CAAC,QAAQ,EAAEJ,MAAM,CAACM,MAAM,CAAC;IAE9D,MAAMI,GAAG,GAAGd,YAAY,CAAC+C,2BAA2B,CAACtB,OAAO,CAAC,KAAK,EAAEiB,WAAW,CAAC;IAChF,OAAOzC,GAAG,CAACgB,GAAG,CAAC,GAAGH,GAAG,IAAIT,WAAW,CAACW,QAAQ,CAAC,CAAC,EAAE,CAAC;EACpD,CAAC;EAED;EACAgC,sBAAsB,EAAEA,CAACN,WAAW,EAAEO,OAAO,KAAK;IAChD,MAAMnC,GAAG,GAAGd,YAAY,CAACkD,yBAAyB,CAACzB,OAAO,CAAC,KAAK,EAAEiB,WAAW,CAAC;IAC9E,OAAOzC,GAAG,CAACmB,IAAI,CAACN,GAAG,EAAE;MAAEmC;IAAQ,CAAC,CAAC;EACnC;AACF,CAAC;AAED,eAAe/C,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}