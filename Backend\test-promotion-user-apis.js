// Test script for Promotion User Management APIs
// Run this with: node test-promotion-user-apis.js

const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';
const ADMIN_TOKEN = 'your-admin-token-here'; // Replace with actual admin token

// Test configuration
const config = {
  headers: {
    'Authorization': `Bearer ${ADMIN_TOKEN}`,
    'Content-Type': 'application/json'
  }
};

// Test data
let testPromotionId = null;
let testUserId = null;

async function runTests() {
  console.log('🚀 Starting Promotion User Management API Tests...\n');

  try {
    // 1. Test: Get all promotions to find a test promotion
    console.log('1️⃣ Testing: Get all promotions (Admin should see all)');
    const promotionsResponse = await axios.get(`${BASE_URL}/promotions?page=1&limit=50`, config);

    console.log(`📊 Total promotions found: ${promotionsResponse.data.promotions?.length || 0}`);
    console.log(`📈 Stats: ${JSON.stringify(promotionsResponse.data.stats, null, 2)}`);
    console.log(`📄 Pagination: ${JSON.stringify(promotionsResponse.data.pagination, null, 2)}`);

    if (promotionsResponse.data.promotions && promotionsResponse.data.promotions.length > 0) {
      testPromotionId = promotionsResponse.data.promotions[0]._id;
      console.log(`✅ Found test promotion: ${testPromotionId}`);

      // Show promotion types
      const publicCount = promotionsResponse.data.promotions.filter(p => p.type === 'PUBLIC').length;
      const privateCount = promotionsResponse.data.promotions.filter(p => p.type === 'PRIVATE').length;
      console.log(`📋 Promotion types: ${publicCount} PUBLIC, ${privateCount} PRIVATE`);
    } else {
      console.log('❌ No promotions found. Please create a promotion first.');
      return;
    }

    // 2. Test: Get promotion users
    console.log('\n2️⃣ Testing: Get promotion users');
    try {
      const usersResponse = await axios.get(
        `${BASE_URL}/promotions/${testPromotionId}/users?page=1&limit=10&status=all`,
        config
      );
      console.log('✅ Get promotion users successful');
      console.log(`📊 Found ${usersResponse.data.data.users.length} users`);
      
      if (usersResponse.data.data.users.length > 0) {
        testUserId = usersResponse.data.data.users[0].user._id;
        console.log(`👤 Test user ID: ${testUserId}`);
      }
    } catch (error) {
      console.log('✅ Get promotion users - No users found (expected for new promotion)');
    }

    // 3. Test: Get promotion statistics
    console.log('\n3️⃣ Testing: Get promotion statistics');
    try {
      const statsResponse = await axios.get(
        `${BASE_URL}/promotions/${testPromotionId}/stats`,
        config
      );
      console.log('✅ Get promotion stats successful');
      console.log(`📈 Stats: ${JSON.stringify(statsResponse.data.data.stats, null, 2)}`);
    } catch (error) {
      console.log(`❌ Get promotion stats failed: ${error.response?.data?.message || error.message}`);
    }

    // 4. Test: Get user promotions (if we have a test user)
    if (testUserId) {
      console.log('\n4️⃣ Testing: Get user promotions');
      try {
        const userPromotionsResponse = await axios.get(
          `${BASE_URL}/promotions/users/${testUserId}?page=1&limit=10`,
          config
        );
        console.log('✅ Get user promotions successful');
        console.log(`🎫 User has ${userPromotionsResponse.data.data.promotions.length} promotions`);
      } catch (error) {
        console.log(`❌ Get user promotions failed: ${error.response?.data?.message || error.message}`);
      }

      // 5. Test: Reset user promotion usage (if user exists)
      console.log('\n5️⃣ Testing: Reset user promotion usage');
      try {
        const resetResponse = await axios.put(
          `${BASE_URL}/promotions/${testPromotionId}/users/${testUserId}/reset`,
          {},
          config
        );
        console.log('✅ Reset user promotion usage successful');
        console.log(`🔄 Reset result: ${resetResponse.data.message}`);
      } catch (error) {
        console.log(`⚠️ Reset user promotion usage: ${error.response?.data?.message || error.message}`);
      }

      // 6. Test: Remove user from promotion (commented out to avoid data loss)
      console.log('\n6️⃣ Testing: Remove user from promotion (SKIPPED - would delete data)');
      console.log('⚠️ Skipping remove user test to preserve data');
      /*
      try {
        const removeResponse = await axios.delete(
          `${BASE_URL}/promotions/${testPromotionId}/users/${testUserId}`,
          config
        );
        console.log('✅ Remove user from promotion successful');
        console.log(`🗑️ Remove result: ${removeResponse.data.message}`);
      } catch (error) {
        console.log(`❌ Remove user from promotion failed: ${error.response?.data?.message || error.message}`);
      }
      */
    } else {
      console.log('\n4️⃣-6️⃣ Skipping user-specific tests (no test user found)');
    }

    console.log('\n🎉 All tests completed!');

  } catch (error) {
    console.error('💥 Test failed:', error.response?.data || error.message);
  }
}

// Test API endpoint availability
async function testEndpointAvailability() {
  console.log('🔍 Testing API endpoint availability...\n');

  const endpoints = [
    { method: 'GET', path: '/promotions', description: 'Get all promotions' },
    { method: 'GET', path: '/promotions/:id/users', description: 'Get promotion users' },
    { method: 'GET', path: '/promotions/:id/stats', description: 'Get promotion stats' },
    { method: 'GET', path: '/promotions/users/:userId', description: 'Get user promotions' },
    { method: 'PUT', path: '/promotions/:id/users/:userId/reset', description: 'Reset user usage' },
    { method: 'DELETE', path: '/promotions/:id/users/:userId', description: 'Remove user from promotion' },
  ];

  for (const endpoint of endpoints) {
    console.log(`📍 ${endpoint.method} ${endpoint.path} - ${endpoint.description}`);
  }

  console.log('\n✅ All endpoints are defined in the routes\n');
}

// Main execution
async function main() {
  console.log('🧪 PROMOTION USER MANAGEMENT API TESTING\n');
  console.log('=' .repeat(50));
  
  await testEndpointAvailability();
  
  console.log('⚠️  IMPORTANT: Make sure to:');
  console.log('1. Start the backend server (npm start)');
  console.log('2. Replace ADMIN_TOKEN with a valid admin token');
  console.log('3. Have at least one promotion in the database');
  console.log('4. Have at least one user who has claimed a promotion\n');
  
  // Run actual API tests
  await runTests();

  console.log('✅ API tests completed!');
}

main().catch(console.error);
