const AuthActions = {
  LOGIN: "<PERSON><PERSON><PERSON><PERSON>",
  <PERSON><PERSON><PERSON><PERSON>_SUCCESS: "LOGIN_SUCCESS",
  L<PERSON>GIN_FAILURE: "LOGIN_FAILURE",
  <PERSON><PERSON><PERSON><PERSON>_GOOGLE: "<PERSON><PERSON><PERSON><PERSON>_GOOGLE",
  <PERSON><PERSON><PERSON><PERSON>_GOOGLE_SUCCESS: "<PERSON>O<PERSON>N_GOOGLE_SUCCESS",
  LOGIN_GOOGLE_FAILURE: "LO<PERSON>N_GOOGLE_FAILURE",
  R<PERSON>ISTER: "REGISTER",
  REGISTER_SUCCESS: "REGISTER_SUCCESS",
  REGISTER_FAILURE: "REGISTER_FAILURE",
  VERIFY_EMAIL: "VERIFY_EMAIL",
  VERIFY_EMAIL_SUCCESS: "VERIFY_EMAIL_SUCCESS",
  VERIFY_EMAIL_FAILURE: "VERIFY_EMAIL_FAILURE",
  RESEND_VERIFICATION: "RESEND_VERIFICATION",
  RESEND_VERIFICATION_SUCCESS: "RESEND_VERIFICATION_SUCCESS",
  RESEND_VERIFICATION_FAILURE: "RESEND_VERIFICATION_FAILURE",

  LOGOUT: "LOGOUT",
  SET_USER: "SET_USER",
  
  UPDATE_PROFILE: "UPDATE_PROFILE",
  UPDATE_PROFILE_SUCCESS: "UPDATE_PROFILE_SUCCESS",

  CHANGE_PASSWORD: "CHANGE_PASSWORD",
  CHANGE_PASSWORD_SUCCESS: "CHANGE_PASSWORD_SUCCESS",

  UPDATE_AVATAR: "UPDATE_AVATAR",
  UPDATE_AVATAR_SUCCESS: "UPDATE_AVATAR_SUCCESS",

  REMOVE_FAVORITE_HOTEL_REQUEST: "REMOVE_FAVORITE_HOTEL_REQUEST",
  REMOVE_FAVORITE_HOTEL_SUCCESS:"REMOVE_FAVORITE_HOTEL_SUCCESS",

  ADD_FAVORITE_HOTEL_REQUEST: "ADD_FAVORITE_HOTEL_REQUEST",
  ADD_FAVORITE_HOTEL_SUCCESS:"ADD_FAVORITE_HOTEL_SUCCESS",
  GET_ALL_REFUND:"GET_ALL_REFUND",
  GET_REFUNDING_SUCCESS:"GET_REFUNDING_SUCCESS",
  REFUNDING:"REFUNDING",
  REFUNDING_SUCCESS:"REFUNDING_SUCCESS"
};
export default AuthActions;
