// Debug script to check promotion counts directly from database
const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/uroom', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Import models
const Promotion = require('./src/models/Promotion');

async function debugPromotions() {
  try {
    console.log('🔍 PROMOTION DEBUG ANALYSIS\n');
    console.log('=' .repeat(50));

    // 1. Total count
    const totalCount = await Promotion.countDocuments();
    console.log(`📊 Total promotions in database: ${totalCount}`);

    // 2. Count by type
    const publicCount = await Promotion.countDocuments({ type: 'PUBLIC' });
    const privateCount = await Promotion.countDocuments({ type: 'PRIVATE' });
    console.log(`📋 PUBLIC promotions: ${publicCount}`);
    console.log(`📋 PRIVATE promotions: ${privateCount}`);

    // 3. Count by status
    const now = new Date();
    const activeCount = await Promotion.countDocuments({
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now }
    });
    const inactiveCount = await Promotion.countDocuments({ isActive: false });
    const expiredCount = await Promotion.countDocuments({ endDate: { $lt: now } });
    const upcomingCount = await Promotion.countDocuments({
      isActive: true,
      startDate: { $gt: now }
    });

    console.log(`\n📈 Status breakdown:`);
    console.log(`   Active: ${activeCount}`);
    console.log(`   Inactive: ${inactiveCount}`);
    console.log(`   Expired: ${expiredCount}`);
    console.log(`   Upcoming: ${upcomingCount}`);

    // 4. Detailed breakdown
    console.log(`\n📋 Detailed breakdown:`);
    
    const publicActive = await Promotion.countDocuments({
      type: 'PUBLIC',
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now }
    });
    const privateActive = await Promotion.countDocuments({
      type: 'PRIVATE',
      isActive: true,
      startDate: { $lte: now },
      endDate: { $gte: now }
    });

    console.log(`   PUBLIC + Active: ${publicActive}`);
    console.log(`   PRIVATE + Active: ${privateActive}`);

    // 5. Sample promotions
    console.log(`\n📋 Sample promotions:`);
    const samplePromotions = await Promotion.find()
      .select('code name type isActive startDate endDate')
      .limit(10)
      .sort({ createdAt: -1 });

    samplePromotions.forEach((promo, index) => {
      const status = now < new Date(promo.startDate) ? 'upcoming' :
                    now > new Date(promo.endDate) ? 'expired' :
                    !promo.isActive ? 'inactive' : 'active';
      
      console.log(`   ${index + 1}. ${promo.code} (${promo.type}) - ${status}`);
    });

    // 6. Test filters that admin would use
    console.log(`\n🔍 Testing admin filters:`);
    
    // Filter 1: All promotions (what admin should see)
    const adminAllFilter = {};
    const adminAllCount = await Promotion.countDocuments(adminAllFilter);
    console.log(`   Admin "all" filter: ${adminAllCount} promotions`);

    // Filter 2: Customer filter (PUBLIC only)
    const customerFilter = { type: 'PUBLIC' };
    const customerCount = await Promotion.countDocuments(customerFilter);
    console.log(`   Customer filter (PUBLIC only): ${customerCount} promotions`);

    // Filter 3: Admin with status=all (should be same as adminAllFilter)
    const adminStatusAllFilter = {}; // No additional filters for status=all
    const adminStatusAllCount = await Promotion.countDocuments(adminStatusAllFilter);
    console.log(`   Admin status="all": ${adminStatusAllCount} promotions`);

    console.log('\n' + '=' .repeat(50));
    console.log('✅ Debug analysis complete!');

  } catch (error) {
    console.error('❌ Error during debug:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run debug
debugPromotions();
