import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import * as Routers from "./utils/Routes";
import Utils from "@utils/Utils";

// Customer pages
import BannedPage from "./pages/BannedPage";
import ErrorPage from "./pages/ErrorPage";
import LoginPage from "./pages/customer/login_register/LoginPage";
import RegisterPage from "./pages/customer/login_register/RegisterPage";
import ForgetPasswordPage from "./pages/customer/login_register/ForgetPasswordPage";
import VerifyCodePage from "./pages/customer/login_register/VerifyCodePage";
import ResetPasswordPage from "./pages/customer/login_register/ResetPasswordPage";
import VerifyCodeRegisterPage from "./pages/customer/login_register/VerifyCodeRegisterPage";

import MyAccountPage from "./pages/customer/information/MyAccountPage";
import BookingBill from "./pages/customer/information/BookingBill";
import CreateFeedback from "./pages/customer/information/CreateFeedback";

import Home from "./pages/customer/home/<USER>";
import Home_detail from "./pages/customer/home/<USER>";
import HotelSearchPage from "./pages/customer/home/<USER>";
import RoomDetailPage from "./pages/customer/home/<USER>";
import BookingCheckPage from "./pages/customer/home/<USER>";
import PaymentPage from "./pages/customer/home/<USER>";
import PaymentSuccessPage from "./pages/customer/home/<USER>";
import PaymentFailedPage from "./pages/customer/home/<USER>";
import ReportedFeedback from "./pages/customer/home/<USER>";
import ChatPage from "./pages/customer/home/<USER>";

import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "@redux/store";
import { initializeSocket } from "@redux/socket/socketSlice";

function App() {
  useEffect(() => {
    document.title = "Uroom Customer";
  }, []);

  const dispatch = useAppDispatch();
  const Socket = useAppSelector((state) => state.Socket.socket);
  const Auth = useAppSelector((state) => state.Auth.Auth);

  useEffect(() => {
    if (Auth?._id === -1) return;
    dispatch(initializeSocket());
  }, [Auth?._id]);

  useEffect(() => {
    if (!Socket) return;
    if (Auth?._id === -1) return;

    console.log("Socket initialized:", Socket.id);
    Socket.emit("register", Auth._id);

    const handleForceJoinRoom = ({ roomId, partnerId }) => {
      Socket.emit("join-room", {
        userId: Auth._id,
        partnerId,
      });
    };

    Socket.on("force-join-room", handleForceJoinRoom);

    return () => {
      Socket.off("force-join-room", handleForceJoinRoom);
    };
  }, [Socket, Auth?._id]);

  return (
    <Router>
      <Routes>
        {/* Auth */}
        <Route path={Routers.LoginPage} element={<LoginPage />} />
        <Route path={Routers.RegisterPage} element={<RegisterPage />} />
        <Route
          path={Routers.VerifyCodeRegisterPage}
          element={<VerifyCodeRegisterPage />}
        />
        <Route
          path={Routers.ForgetPasswordPage}
          element={<ForgetPasswordPage />}
        />
        <Route path={Routers.VerifyCodePage} element={<VerifyCodePage />} />
        <Route
          path={Routers.ResetPasswordPage}
          element={<ResetPasswordPage />}
        />

        {/* User Info */}
        <Route
          path={`${Routers.MyAccountPage}/:section`}
          element={<MyAccountPage />}
        />
        <Route path={`${Routers.BookingBill}/:id`} element={<BookingBill />} />
        <Route
          path={`${Routers.CreateFeedback}/:id`}
          element={<CreateFeedback />}
        />

        {/* Home */}
        <Route path={Routers.Home} element={<Home />} />
        <Route path={Routers.HotelSearchPage} element={<HotelSearchPage />} />
        <Route path={`${Routers.Home_detail}/:id`} element={<Home_detail />} />
        <Route
          path={`${Routers.RoomDetailPage}/:id`}
          element={<RoomDetailPage />}
        />
        <Route path={Routers.BookingCheckPage} element={<BookingCheckPage />} />
        <Route
          path={`${Routers.ReportedFeedback}/:id`}
          element={<ReportedFeedback />}
        />
        <Route path={Routers.PaymentPage} element={<PaymentPage />} />
        <Route
          path={Routers.PaymentSuccessPage}
          element={<PaymentSuccessPage />}
        />
        <Route
          path={Routers.PaymentFailedPage}
          element={<PaymentFailedPage />}
        />

        {/* Others */}
        <Route path={Routers.BannedPage} element={<BannedPage />} />
        <Route path={Routers.ErrorPage} element={<ErrorPage />} />
        <Route path={Routers.ChatPage} element={<ChatPage />} />
      </Routes>
    </Router>
  );
}

export default App;
