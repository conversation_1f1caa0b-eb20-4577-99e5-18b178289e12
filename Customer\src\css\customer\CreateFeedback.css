C.feedback-card {
    max-width: 800px;
    margin: 0 auto;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .back-button {
    color: #000;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 5px;
  }
  
  .back-button:hover {
    color: #0056b3;
  }
  
  .star-rating {
    display: flex;
    gap: 5px;
  }
  
  .star-rating input[type="radio"] {
    display: none;
  }
  
  .star {
    cursor: pointer;
    transition: color 200ms;
    font-size: 24px;
  }
  
  .char-count {
    position: absolute;
    bottom: 8px;
    right: 8px;
    color: #6c757d;
  }
  
  .form-control {
    border-radius: 4px;
    resize: none;
  }
  
  .form-control:focus {
    border-color: #0056b3;
    box-shadow: 0 0 0 0.2rem rgba(0, 86, 179, 0.25);
  }
  
  /* Button styling */
  .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
  }
  
  .btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
  }
  
  