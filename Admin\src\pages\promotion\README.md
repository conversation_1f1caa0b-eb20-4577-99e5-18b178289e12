# Promotion User Management

This document describes the new Promotion User Management features added to the admin panel.

## Overview

The Promotion User Management system allows administrators to:
- View users who have claimed/used specific promotions
- Monitor promotion usage statistics
- Manage user-specific promotion data
- Reset user usage counts
- Remove users from promotions

## New Components

### 1. PromotionUsersModal
**Location**: `Admin/src/pages/promotion/PromotionUsersModal.jsx`

A modal component that displays all users associated with a specific promotion.

**Features**:
- Paginated user list with search functionality
- Filter by status (claimed, used, all)
- Sort by various fields (name, usage count, claimed date, etc.)
- Actions: Reset usage count, Remove user from promotion
- Real-time status indicators

**Props**:
- `show` (boolean): Controls modal visibility
- `onHide` (function): Callback when modal is closed
- `promotion` (object): Promotion data

### 2. PromotionStatsCard
**Location**: `Admin/src/pages/promotion/PromotionStatsCard.jsx`

A card component that displays comprehensive statistics for a promotion.

**Features**:
- Overview statistics (total users, claimed, used, average usage)
- Claim rate and usage rate progress bars
- Status breakdown (not claimed, claimed not used, active, used up)
- Recent activity (30-day statistics)
- Usage distribution table
- Promotion limits information

**Props**:
- `promotion` (object): Promotion data

## Updated Components

### 1. ListPromotionPage
**Location**: `Admin/src/pages/promotion/ListPromotionPage.jsx`

**New Features**:
- "View Users" button for each promotion
- "View Statistics" button for each promotion
- Integration with PromotionUsersModal and PromotionStatsCard

### 2. DetailPromotionPage
**Location**: `Admin/src/pages/promotion/DetailPromotionPage.jsx`

**New Features**:
- Added tabs for "Details" and "Statistics"
- Statistics tab shows PromotionStatsCard when in view mode
- Enhanced UI with better organization

## API Endpoints

### Backend Routes
**Location**: `Backend/src/route_controller/Promotion/PromotionRoute.js`

New admin-only routes:
- `GET /promotions/:id/users` - Get users for a promotion
- `GET /promotions/:id/stats` - Get promotion statistics
- `GET /promotions/users/:userId` - Get promotions for a user
- `DELETE /promotions/:id/users/:userId` - Remove user from promotion
- `PUT /promotions/:id/users/:userId/reset` - Reset user usage count

### Controller Methods
**Location**: `Backend/src/route_controller/Promotion/PromotionController.js`

New methods:
- `getPromotionUsers()` - Paginated list of promotion users
- `getPromotionStats()` - Comprehensive promotion statistics
- `getUserPromotions()` - All promotions for a specific user
- `removeUserFromPromotion()` - Remove user-promotion association
- `resetUserPromotionUsage()` - Reset user's usage count

## Redux Integration

### Actions
**Location**: `Admin/src/redux/promotion/actions.js`

New action types:
- `GET_PROMOTION_USERS` / `_SUCCESS` / `_FAILURE`
- `GET_PROMOTION_STATS` / `_SUCCESS` / `_FAILURE`
- `GET_USER_PROMOTIONS` / `_SUCCESS` / `_FAILURE`
- `REMOVE_USER_FROM_PROMOTION` / `_SUCCESS` / `_FAILURE`
- `RESET_USER_PROMOTION_USAGE` / `_SUCCESS` / `_FAILURE`

### Reducer
**Location**: `Admin/src/redux/promotion/reducer.js`

New state structure:
```javascript
{
  promotionUsers: {
    data: [],
    loading: false,
    error: null,
    pagination: { ... },
    filters: { ... }
  },
  promotionStats: {
    data: null,
    loading: false,
    error: null
  },
  userPromotions: {
    data: [],
    user: null,
    loading: false,
    error: null,
    pagination: { ... }
  },
  removingUser: false,
  resettingUsage: false
}
```

### Sagas
**Location**: `Admin/src/redux/promotion/saga.js`

New saga functions:
- `getPromotionUsers()`
- `getPromotionStats()`
- `getUserPromotions()`
- `removeUserFromPromotion()`
- `resetUserPromotionUsage()`

### Factories
**Location**: `Admin/src/redux/promotion/factories.js`

New API call functions:
- `getPromotionUsers(id, params)`
- `getPromotionStats(id)`
- `getUserPromotions(userId, params)`
- `removeUserFromPromotion(promotionId, userId)`
- `resetUserPromotionUsage(promotionId, userId)`

## Styling

### CSS Classes
**Location**: `Admin/src/pages/promotion/promotion.css`

New styles for:
- `.users-btn`, `.stats-btn` - Action button styling
- `.stat-card` - Statistics card styling
- `.status-breakdown`, `.recent-activity` - Status display styling
- `.promotion-users-modal` - Modal-specific styling
- `.promotion-limits` - Limits information styling

## Usage Examples

### Viewing Promotion Users
1. Navigate to Promotion Management
2. Click the "Users" button (👥) for any promotion
3. Use search and filters to find specific users
4. View user details, usage counts, and status

### Viewing Promotion Statistics
1. Navigate to Promotion Management
2. Click the "Statistics" button (📊) for any promotion
3. View comprehensive statistics and usage patterns

### Managing User Data
1. Open the Users modal for a promotion
2. Use "Reset Usage" (🔄) to reset a user's usage count
3. Use "Remove User" (🗑️) to remove user from promotion

### Viewing Detailed Statistics
1. Click "View Details" (👁️) for any promotion
2. Switch to the "Statistics" tab
3. View detailed analytics and usage patterns

## Testing

### Test File
**Location**: `Backend/test-promotion-user-apis.js`

Run tests with:
```bash
cd Backend
node test-promotion-user-apis.js
```

**Prerequisites**:
- Backend server running
- Valid admin token
- At least one promotion in database
- At least one user with promotion data

## Security

All new endpoints require admin authentication:
- Uses `isAdmin` middleware
- Validates user permissions
- Protects sensitive user data

## Performance Considerations

- Pagination implemented for large user lists
- Efficient database queries with proper indexing
- Lazy loading of statistics data
- Optimized Redux state management

## Future Enhancements

Potential improvements:
- Export user data to CSV/Excel
- Bulk user management operations
- Advanced analytics and reporting
- Email notifications for promotion events
- Integration with customer support tools
