{"ast": null, "code": "var _jsxFileName = \"E:\\\\Uroom\\\\Customer\\\\src\\\\pages\\\\customer\\\\home\\\\PaymentFailedPage.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { <PERSON>, <PERSON><PERSON>, Spinner } from \"react-bootstrap\";\nimport { XCircle, ArrowLeft } from \"react-feather\";\nimport \"bootstrap/dist/css/bootstrap.min.css\";\nimport Banner from \"../../../images/banner.jpg\";\nimport { useNavigate, useSearchParams } from \"react-router-dom\";\nimport Footer from \"../Footer\";\nimport Header from \"../Header\";\nimport * as Routers from \"../../../utils/Routes\";\nimport Factories from \"@redux/search/factories\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PaymentFailedPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const reservationId = searchParams.get(\"reservationId\");\n  console.log(\"reservationId\", reservationId);\n\n  // Auto-cancel reservation when payment failed\n  useEffect(() => {\n    const cancelReservation = async () => {\n      if (reservationId) {\n        try {\n          console.log(\"Auto-canceling reservation due to payment failure:\", reservationId);\n          const response = await Factories.cancel_payment(reservationId);\n          if ((response === null || response === void 0 ? void 0 : response.status) === 200) {\n            console.log(\"Reservation canceled successfully\");\n          }\n        } catch (error) {\n          console.error(\"Error auto-canceling reservation:\", error);\n        }\n      }\n    };\n    cancelReservation();\n  }, [reservationId]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"d-flex flex-column min-vh-100\",\n    style: {\n      backgroundImage: `url(${Banner})`,\n      backgroundSize: \"cover\",\n      backgroundPosition: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\",\n      style: {\n        paddingTop: \"50px\",\n        paddingBottom: \"50px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        className: \"text-center\",\n        style: {\n          maxWidth: \"800px\",\n          height: \"500px\",\n          width: \"100%\",\n          backgroundColor: \"rgba(255, 255, 255, 0.95)\",\n          borderRadius: \"15px\",\n          boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\n          border: \"none\",\n          padding: \"2rem\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(XCircle, {\n              size: 150,\n              style: {\n                color: \"#e74c3c\",\n                strokeWidth: 2\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"mb-3\",\n            style: {\n              fontWeight: \"700\",\n              color: \"#e74c3c\"\n            },\n            children: \"Payment Failed!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted mb-2\",\n            style: {\n              fontWeight: \"600\",\n              fontSize: 20\n            },\n            children: \"Oops! Your payment was not successful.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mb-4\",\n            style: {\n              fontSize: \"1.2rem\",\n              fontWeight: \"500\"\n            },\n            children: \"Please try again or contact support.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"d-flex justify-content-center mt-5\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"dark\",\n              className: \"px-4 py-3 d-flex align-items-center justify-content-center\",\n              style: {\n                flex: 1,\n                borderRadius: \"8px\",\n                backgroundColor: \"#000\",\n                border: \"none\"\n              },\n              onClick: () => {\n                navigate(Routers.Home);\n              },\n              children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n                size: 20,\n                className: \"me-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), \"Go to home page\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_s(PaymentFailedPage, \"BL0/uIvqgnY+m2kKjm25nXv/8uc=\", false, function () {\n  return [useNavigate, useSearchParams];\n});\n_c = PaymentFailedPage;\nexport default PaymentFailedPage;\nvar _c;\n$RefreshReg$(_c, \"PaymentFailedPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Card", "<PERSON><PERSON>", "Spinner", "XCircle", "ArrowLeft", "Banner", "useNavigate", "useSearchParams", "Footer", "Header", "Routers", "Factories", "jsxDEV", "_jsxDEV", "PaymentFailedPage", "_s", "navigate", "searchParams", "reservationId", "get", "console", "log", "cancelReservation", "response", "cancel_payment", "status", "error", "className", "style", "backgroundImage", "backgroundSize", "backgroundPosition", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paddingTop", "paddingBottom", "max<PERSON><PERSON><PERSON>", "height", "width", "backgroundColor", "borderRadius", "boxShadow", "border", "padding", "Body", "size", "color", "strokeWidth", "fontWeight", "fontSize", "variant", "flex", "onClick", "Home", "_c", "$RefreshReg$"], "sources": ["E:/Uroom/Customer/src/pages/customer/home/<USER>"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { <PERSON>, <PERSON><PERSON>, Spinner } from \"react-bootstrap\";\r\nimport { <PERSON>Circle, ArrowLeft } from \"react-feather\";\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport Banner from \"../../../images/banner.jpg\";\r\nimport { useNavigate, useSearchParams } from \"react-router-dom\";\r\nimport Footer from \"../Footer\";\r\nimport Header from \"../Header\";\r\nimport * as Routers from \"../../../utils/Routes\";\r\nimport Factories from \"@redux/search/factories\";\r\n\r\nconst PaymentFailedPage = () => {\r\n  const navigate = useNavigate();\r\n  const [searchParams] = useSearchParams();\r\n  const reservationId = searchParams.get(\"reservationId\");\r\n  console.log(\"reservationId\", reservationId);\r\n\r\n  // Auto-cancel reservation when payment failed\r\n  useEffect(() => {\r\n    const cancelReservation = async () => {\r\n      if (reservationId) {\r\n        try {\r\n          console.log(\"Auto-canceling reservation due to payment failure:\", reservationId);\r\n          const response = await Factories.cancel_payment(reservationId);\r\n          if (response?.status === 200) {\r\n            console.log(\"Reservation canceled successfully\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error auto-canceling reservation:\", error);\r\n        }\r\n      }\r\n    };\r\n\r\n    cancelReservation();\r\n  }, [reservationId]);\r\n\r\n  return (\r\n    <div\r\n      className=\"d-flex flex-column min-vh-100\"\r\n      style={{\r\n        backgroundImage: `url(${Banner})`,\r\n        backgroundSize: \"cover\",\r\n        backgroundPosition: \"center\",\r\n      }}\r\n    >\r\n      <Header />\r\n      <div\r\n        className=\"flex-grow-1 d-flex align-items-center justify-content-center content-wrapper\"\r\n        style={{ paddingTop: \"50px\", paddingBottom: \"50px\" }}\r\n      >\r\n        <Card\r\n          className=\"text-center\"\r\n          style={{\r\n            maxWidth: \"800px\",\r\n            height: \"500px\",\r\n            width: \"100%\",\r\n            backgroundColor: \"rgba(255, 255, 255, 0.95)\",\r\n            borderRadius: \"15px\",\r\n            boxShadow: \"0 4px 6px rgba(0, 0, 0, 0.1)\",\r\n            border: \"none\",\r\n            padding: \"2rem\",\r\n          }}\r\n        >\r\n          <Card.Body>\r\n            <div className=\"mb-4\">\r\n              <XCircle\r\n                size={150}\r\n                style={{\r\n                  color: \"#e74c3c\",\r\n                  strokeWidth: 2,\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            <h2\r\n              className=\"mb-3\"\r\n              style={{ fontWeight: \"700\", color: \"#e74c3c\" }}\r\n            >\r\n              Payment Failed!\r\n            </h2>\r\n\r\n            <p\r\n              className=\"text-muted mb-2\"\r\n              style={{ fontWeight: \"600\", fontSize: 20 }}\r\n            >\r\n              Oops! Your payment was not successful.\r\n            </p>\r\n            <p\r\n              className=\"mb-4\"\r\n              style={{ fontSize: \"1.2rem\", fontWeight: \"500\" }}\r\n            >\r\n              Please try again or contact support.\r\n            </p>\r\n\r\n            <div className=\"d-flex justify-content-center mt-5\">\r\n              <Button\r\n                variant=\"dark\"\r\n                className=\"px-4 py-3 d-flex align-items-center justify-content-center\"\r\n                style={{\r\n                  flex: 1,\r\n                  borderRadius: \"8px\",\r\n                  backgroundColor: \"#000\",\r\n                  border: \"none\",\r\n                }}\r\n                onClick={() => {\r\n                  navigate(Routers.Home);\r\n                }}\r\n              >\r\n                <ArrowLeft size={20} className=\"me-2\" />\r\n                Go to home page\r\n              </Button>\r\n            </div>\r\n          </Card.Body>\r\n        </Card>\r\n      </div>\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PaymentFailedPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,QAAQ,iBAAiB;AACvD,SAASC,OAAO,EAAEC,SAAS,QAAQ,eAAe;AAClD,OAAO,sCAAsC;AAC7C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC/D,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAO,KAAKC,OAAO,MAAM,uBAAuB;AAChD,OAAOC,SAAS,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACW,YAAY,CAAC,GAAGV,eAAe,CAAC,CAAC;EACxC,MAAMW,aAAa,GAAGD,YAAY,CAACE,GAAG,CAAC,eAAe,CAAC;EACvDC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEH,aAAa,CAAC;;EAE3C;EACApB,SAAS,CAAC,MAAM;IACd,MAAMwB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAIJ,aAAa,EAAE;QACjB,IAAI;UACFE,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAEH,aAAa,CAAC;UAChF,MAAMK,QAAQ,GAAG,MAAMZ,SAAS,CAACa,cAAc,CAACN,aAAa,CAAC;UAC9D,IAAI,CAAAK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,MAAK,GAAG,EAAE;YAC5BL,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAClD;QACF,CAAC,CAAC,OAAOK,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAC3D;MACF;IACF,CAAC;IAEDJ,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACJ,aAAa,CAAC,CAAC;EAEnB,oBACEL,OAAA;IACEc,SAAS,EAAC,+BAA+B;IACzCC,KAAK,EAAE;MACLC,eAAe,EAAE,OAAOxB,MAAM,GAAG;MACjCyB,cAAc,EAAE,OAAO;MACvBC,kBAAkB,EAAE;IACtB,CAAE;IAAAC,QAAA,gBAEFnB,OAAA,CAACJ,MAAM;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVvB,OAAA;MACEc,SAAS,EAAC,8EAA8E;MACxFC,KAAK,EAAE;QAAES,UAAU,EAAE,MAAM;QAAEC,aAAa,EAAE;MAAO,CAAE;MAAAN,QAAA,eAErDnB,OAAA,CAACb,IAAI;QACH2B,SAAS,EAAC,aAAa;QACvBC,KAAK,EAAE;UACLW,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,OAAO;UACfC,KAAK,EAAE,MAAM;UACbC,eAAe,EAAE,2BAA2B;UAC5CC,YAAY,EAAE,MAAM;UACpBC,SAAS,EAAE,8BAA8B;UACzCC,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;QACX,CAAE;QAAAd,QAAA,eAEFnB,OAAA,CAACb,IAAI,CAAC+C,IAAI;UAAAf,QAAA,gBACRnB,OAAA;YAAKc,SAAS,EAAC,MAAM;YAAAK,QAAA,eACnBnB,OAAA,CAACV,OAAO;cACN6C,IAAI,EAAE,GAAI;cACVpB,KAAK,EAAE;gBACLqB,KAAK,EAAE,SAAS;gBAChBC,WAAW,EAAE;cACf;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENvB,OAAA;YACEc,SAAS,EAAC,MAAM;YAChBC,KAAK,EAAE;cAAEuB,UAAU,EAAE,KAAK;cAAEF,KAAK,EAAE;YAAU,CAAE;YAAAjB,QAAA,EAChD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELvB,OAAA;YACEc,SAAS,EAAC,iBAAiB;YAC3BC,KAAK,EAAE;cAAEuB,UAAU,EAAE,KAAK;cAAEC,QAAQ,EAAE;YAAG,CAAE;YAAApB,QAAA,EAC5C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJvB,OAAA;YACEc,SAAS,EAAC,MAAM;YAChBC,KAAK,EAAE;cAAEwB,QAAQ,EAAE,QAAQ;cAAED,UAAU,EAAE;YAAM,CAAE;YAAAnB,QAAA,EAClD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJvB,OAAA;YAAKc,SAAS,EAAC,oCAAoC;YAAAK,QAAA,eACjDnB,OAAA,CAACZ,MAAM;cACLoD,OAAO,EAAC,MAAM;cACd1B,SAAS,EAAC,4DAA4D;cACtEC,KAAK,EAAE;gBACL0B,IAAI,EAAE,CAAC;gBACPX,YAAY,EAAE,KAAK;gBACnBD,eAAe,EAAE,MAAM;gBACvBG,MAAM,EAAE;cACV,CAAE;cACFU,OAAO,EAAEA,CAAA,KAAM;gBACbvC,QAAQ,CAACN,OAAO,CAAC8C,IAAI,CAAC;cACxB,CAAE;cAAAxB,QAAA,gBAEFnB,OAAA,CAACT,SAAS;gBAAC4C,IAAI,EAAE,EAAG;gBAACrB,SAAS,EAAC;cAAM;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAE1C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNvB,OAAA,CAACL,MAAM;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACrB,EAAA,CA3GID,iBAAiB;EAAA,QACJR,WAAW,EACLC,eAAe;AAAA;AAAAkD,EAAA,GAFlC3C,iBAAiB;AA6GvB,eAAeA,iBAAiB;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}